# 工程师角色权限测试指南

## 测试目标
验证工程师角色只能访问维修相关功能，不能访问设备管理功能。

## 测试步骤

### 1. 登录测试
1. 打开小程序，进入登录页面
2. 点击"工程师模拟登录"按钮
3. 验证登录成功，跳转到首页
4. 确认用户角色显示为"工程师"

### 2. 首页功能测试
1. 验证首页显示工程师专用的统计卡片：
   - 待接单
   - 进行中
   - 今日完成
   - 总完成
2. 验证首页显示工程师专用的快捷操作：
   - 工单管理
   - 扫码处理
   - 客户沟通
   - 配件申请
3. 验证不显示设备相关的统计和操作

### 3. 底部导航测试
1. 验证可以访问的页面：
   - ✅ 首页
   - ❌ 设备（应显示权限提示）
   - ✅ 工单
   - ✅ 消息
   - ✅ 我的
2. 点击"设备"tab，应显示权限受限提示并返回首页

### 4. 工单页面测试
1. 进入工单页面
2. 验证显示工程师专用的标签页：
   - 全部
   - 待接单
   - 进行中
   - 已完成
3. 验证可以正常查看和处理工单

### 5. 聊天页面测试
1. 进入消息页面
2. 验证可以正常查看工单相关的聊天记录
3. 验证自己发送的消息显示在右侧，包含头像和昵称

### 6. 个人资料页面测试
1. 进入"我的"页面
2. 验证显示工程师专用的统计：
   - 分配工单
   - 已完成
   - 未读消息
3. 验证不显示"管理设备"统计
4. 点击统计项时不会跳转到设备页面

### 7. 扫码功能测试
1. 在首页点击扫码功能
2. 扫描设备二维码时，应显示提示信息
3. 引导用户查看相关工单而不是设备详情

## 预期结果

### ✅ 工程师可以访问的功能：
- 首页（工程师专用视图）
- 工单管理（查看分配给自己的工单）
- 消息中心（与工单相关的沟通）
- 个人资料（工程师专用统计）
- 工单详情和处理
- 聊天功能

### ❌ 工程师不能访问的功能：
- 设备页面（显示权限提示）
- 设备详情页面
- 添加/编辑设备
- 设备相关的统计信息
- 设备管理相关的快捷操作

## 错误处理验证
1. 访问受限页面时显示友好的提示信息
2. 提供合理的替代操作建议
3. 不会导致程序崩溃或异常

## 注意事项
- 工程师角色专注于维修工作流程
- 所有设备相关的功能都应该被限制
- 提供清晰的权限提示和引导
- 保持用户体验的一致性
