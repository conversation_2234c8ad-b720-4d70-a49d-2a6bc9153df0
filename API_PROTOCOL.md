# 资管维平台 API 协议文档

## 概述

本文档描述了资管维平台前后端交互的 RESTful API 协议规范，包括认证授权、角色管理、多角色切换等核心功能的接口定义。

## 基础信息

- **Base URL**: `https://api.ziguan-wei.com/api`
- **API Version**: v1.0
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 认证机制

### JWT Token 认证
所有需要认证的接口都需要在请求头中携带 JWT Token：

```
Authorization: Bearer <token>
```

### Token 刷新机制
- Access Token 有效期：7天
- Refresh Token 有效期：30天
- 当 Access Token 过期时，使用 Refresh Token 获取新的 Token

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "details": {
    // 详细错误信息（可选）
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 1. 认证授权接口

### 1.1 微信小程序登录

**接口**: `POST /auth/wechat-login`

**描述**: 通过微信小程序授权码进行登录，支持新用户自动注册

**请求参数**:
```json
{
  "code": "微信授权码",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "openid": "wx_openid",
      "username": "用户名",
      "avatar": "头像URL",
      "roles": ["user"],
      "currentRole": "user",
      "isMultiRole": false,
      "is_verified": false
    },
    "token": "jwt_access_token",
    "refreshToken": "jwt_refresh_token",
    "needRoleSelection": false
  }
}
```

### 1.2 Token 刷新

**接口**: `POST /auth/refresh-token`

**请求参数**:
```json
{
  "refreshToken": "refresh_token"
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "令牌刷新成功",
  "data": {
    "token": "new_access_token",
    "refreshToken": "new_refresh_token"
  }
}
```

### 1.3 实名认证

**接口**: `POST /auth/verify-identity`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "realName": "真实姓名",
  "idCard": "身份证号",
  "phone": "手机号",
  "department": "部门/课题组",
  "employeeId": "工号（可选）"
}
```

## 2. 角色管理接口

### 2.1 获取用户角色信息

**接口**: `GET /users/roles`

**请求头**: `Authorization: Bearer <token>`

**响应数据**:
```json
{
  "success": true,
  "data": {
    "userId": 1,
    "roles": [
      {
        "role": "user",
        "roleName": "普通用户",
        "permissions": ["equipment:read", "order:create"],
        "department": "计算机学院",
        "isActive": true
      },
      {
        "role": "engineer",
        "roleName": "工程师",
        "permissions": ["order:accept", "order:update"],
        "department": "维修部",
        "isActive": true
      }
    ],
    "currentRole": "user",
    "defaultRole": "user"
  }
}
```

### 2.2 切换用户角色

**接口**: `POST /users/switch-role`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "targetRole": "engineer"
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "角色切换成功",
  "data": {
    "currentRole": "engineer",
    "permissions": ["order:accept", "order:update"],
    "token": "new_token_with_role_context"
  }
}
```

### 2.3 为用户添加角色（管理员）

**接口**: `POST /admin/users/{userId}/roles`

**请求头**: `Authorization: Bearer <admin_token>`

**请求参数**:
```json
{
  "role": "engineer",
  "department": "维修部",
  "permissions": ["order:accept", "order:update"]
}
```

## 3. 设备管理接口

### 3.1 获取设备列表

**接口**: `GET /equipment`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）
- `keyword`: 搜索关键词
- `status`: 设备状态
- `department`: 部门筛选

**响应数据**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "设备名称",
        "maintenanceCode": "维护编号",
        "qrCodeUrl": "二维码URL",
        "brand": "品牌",
        "model": "型号",
        "status": "normal",
        "department": "所属部门",
        "owner": {
          "id": 1,
          "name": "设备负责人"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

### 3.2 创建设备

**接口**: `POST /equipment`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "name": "设备名称",
  "maintenanceCode": "维护编号",
  "brandId": 1,
  "modelId": 1,
  "serialNumber": "序列号",
  "purchaseDate": "2024-01-01",
  "isUnderWarranty": true,
  "customFields": {
    "location": "实验室A101"
  }
}
```

## 4. 工单管理接口

### 4.1 创建工单

**接口**: `POST /orders`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "equipmentId": 1,
  "faultDescription": "故障描述",
  "attachments": [
    {
      "fileName": "故障图片.jpg",
      "fileUrl": "https://example.com/image.jpg",
      "fileType": "image"
    }
  ],
  "priority": "normal"
}
```

### 4.2 获取工单列表

**接口**: `GET /orders`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `status`: 工单状态
- `priority`: 优先级
- `assignedTo`: 指派工程师ID
- `dateFrom`: 开始日期
- `dateTo`: 结束日期

**响应数据**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "orderNumber": "WO202401010001",
        "equipment": {
          "id": 1,
          "name": "设备名称",
          "maintenanceCode": "维护编号"
        },
        "status": "pending",
        "priority": "normal",
        "faultDescription": "故障描述",
        "createdBy": {
          "id": 1,
          "name": "报修人"
        },
        "assignedTo": {
          "id": 2,
          "name": "工程师"
        },
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "totalPages": 5
    }
  }
}
```

## 5. 实时通信接口

### 5.1 发送消息

**接口**: `POST /chat/send`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "orderId": 1,
  "receiverId": 2,
  "messageType": "text",
  "content": "消息内容"
}
```

### 5.2 获取消息列表

**接口**: `GET /chat/messages`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:
- `orderId`: 工单ID
- `page`: 页码
- `limit`: 每页数量

## 错误码定义

| 错误码 | 说明 |
|--------|------|
| AUTH_001 | 微信授权码无效 |
| AUTH_002 | Token 已过期 |
| AUTH_003 | Token 无效 |
| AUTH_004 | 权限不足 |
| ROLE_001 | 角色不存在 |
| ROLE_002 | 角色切换失败 |
| ROLE_003 | 用户无此角色权限 |
| USER_001 | 用户不存在 |
| USER_002 | 用户已被禁用 |
| EQUIP_001 | 设备不存在 |
| EQUIP_002 | 维护编号重复 |
| ORDER_001 | 工单不存在 |
| ORDER_002 | 工单状态不允许此操作 |

## 6. 组织管理接口

### 6.1 获取组织列表

**接口**: `GET /organizations`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）
- `keyword`: 搜索关键词
- `type`: 组织类型

**响应数据**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "清华大学",
        "shortName": "清华",
        "type": "university",
        "researchGroupCount": 25
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "totalPages": 5
    }
  }
}
```

### 6.2 获取课题组列表

**接口**: `GET /research-groups`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `organizationId`: 组织ID
- `keyword`: 搜索关键词

**响应数据**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "计算机视觉实验室",
        "organization": {
          "id": 1,
          "name": "清华大学"
        },
        "piList": [
          {
            "id": 1,
            "name": "张教授",
            "phone": "138****1234"
          }
        ],
        "memberCount": 15,
        "equipmentCount": 30
      }
    ]
  }
}
```

## 7. 统计分析接口

### 7.1 获取用户统计数据

**接口**: `GET /users/stats`

**请求头**: `Authorization: Bearer <token>`

**响应数据**:
```json
{
  "success": true,
  "data": {
    "equipmentCount": 10,
    "orderCount": 25,
    "pendingOrders": 3,
    "completedOrders": 20,
    "monthlyTrend": [
      {
        "month": "2024-01",
        "orderCount": 5,
        "completionRate": 0.8
      }
    ]
  }
}
```

### 7.2 获取管理员仪表板数据

**接口**: `GET /admin/dashboard`

**请求头**: `Authorization: Bearer <admin_token>`

**响应数据**:
```json
{
  "success": true,
  "data": {
    "totalUsers": 1000,
    "totalEquipment": 500,
    "totalOrders": 2000,
    "todayOrders": 15,
    "orderTrend": [
      {
        "date": "2024-01-01",
        "count": 10
      }
    ],
    "equipmentStatus": {
      "normal": 450,
      "maintenance": 30,
      "fault": 20
    }
  }
}
```

## 8. 文件上传接口

### 8.1 上传文件

**接口**: `POST /upload`

**请求头**:
- `Authorization: Bearer <token>`
- `Content-Type: multipart/form-data`

**请求参数**:
- `file`: 文件对象
- `type`: 文件类型（image/document/video）
- `category`: 文件分类（equipment/order/user）

**响应数据**:
```json
{
  "success": true,
  "data": {
    "fileId": "file_123456",
    "fileName": "image.jpg",
    "fileUrl": "https://example.com/uploads/image.jpg",
    "fileSize": 1024000,
    "mimeType": "image/jpeg"
  }
}
```

## 9. 通知推送接口

### 9.1 获取通知列表

**接口**: `GET /notifications`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `type`: 通知类型
- `isRead`: 是否已读

**响应数据**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "title": "工单状态更新",
        "content": "您的工单 #WO001 已被工程师接收",
        "type": "order_update",
        "isRead": false,
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "unreadCount": 5
  }
}
```

### 9.2 标记通知为已读

**接口**: `PATCH /notifications/{id}/read`

**请求头**: `Authorization: Bearer <token>`

## 接口版本控制

API 采用 URL 路径版本控制：
- v1: `/api/v1/`
- v2: `/api/v2/`

当前版本为 v1，向后兼容性保证至少维持 6 个月。

## WebSocket 实时通信

### 连接地址
`wss://api.ziguan-wei.com/socket.io`

### 认证
连接时需要在查询参数中传递 token：
`wss://api.ziguan-wei.com/socket.io?token=<jwt_token>`

### 事件类型

#### 客户端发送事件
- `join_room`: 加入聊天室
- `send_message`: 发送消息
- `typing`: 正在输入

#### 服务端推送事件
- `new_message`: 新消息
- `order_update`: 工单状态更新
- `notification`: 系统通知
- `user_typing`: 用户正在输入

### 消息格式
```json
{
  "event": "new_message",
  "data": {
    "orderId": 1,
    "senderId": 1,
    "content": "消息内容",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```
