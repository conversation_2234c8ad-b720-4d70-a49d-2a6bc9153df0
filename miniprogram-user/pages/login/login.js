// pages/login/login.js
const app = getApp();

Page({
  data: {
    loading: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo')
  },

  onLoad() {
    // 检查是否已登录
    if (app.globalData.token && app.globalData.userInfo) {
      this.redirectToHome();
    }
  },

  // 微信登录
  async onWechatLogin() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      let userProfile = null;

      // 如果使用云托管，可以选择性获取用户信息
      // 云托管会自动注入基本的用户标识信息
      if (app.globalData.useCloudContainer) {
        console.log('云托管登录模式');
        // 可以尝试获取用户信息，但不是必须的
        try {
          userProfile = await this.getUserProfile();
          console.log('获取到用户信息:', userProfile.userInfo);
        } catch (error) {
          console.log('用户取消授权或获取用户信息失败，使用云托管注入的信息');
          // 不影响登录流程，云托管会提供基本信息
        }
      } else {
        // 传统模式必须获取用户信息
        userProfile = await this.getUserProfile();
      }

      // 进行登录
      const result = await app.wechatLogin(userProfile);

      if (result.success) {
        // 更新tabBar
        app.updateTabBarByRole();

        if (result.needRoleSelection) {
          // 需要角色选择
          wx.navigateTo({
            url: '/pages/role-select/role-select'
          });
        } else {
          // 直接跳转到首页
          this.redirectToHome();
        }
      }
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      });
    });
  },

  // 跳转到首页
  redirectToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 工程师模拟登录
  async onEngineerLogin() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 模拟工程师用户信息
      const mockEngineerInfo = {
        id: 'engineer_001',
        username: '工程师001',
        real_name: '张工程师',
        avatar: '/images/default-avatar.png',
        role: 'engineer',
        roles: ['engineer'],
        currentRole: 'engineer',
        isMultiRole: false
      };

      // 模拟token
      const mockToken = 'mock_engineer_token_' + Date.now();
      const mockRefreshToken = 'mock_engineer_refresh_token_' + Date.now();

      // 保存到全局数据和本地存储
      app.globalData.token = mockToken;
      app.globalData.refreshToken = mockRefreshToken;
      app.globalData.userInfo = mockEngineerInfo;
      app.globalData.currentRole = 'engineer';
      app.globalData.roles = ['engineer'];
      app.globalData.isMultiRole = false;

      wx.setStorageSync('token', mockToken);
      wx.setStorageSync('refreshToken', mockRefreshToken);
      wx.setStorageSync('userInfo', mockEngineerInfo);

      wx.showToast({
        title: '工程师登录成功',
        icon: 'success'
      });

      // 更新tabBar
      app.updateTabBarByRole();

      // 跳转到首页
      setTimeout(() => {
        this.redirectToHome();
      }, 1500);

    } catch (error) {
      console.error('工程师模拟登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取用户信息（兼容旧版本）
  async onGetUserInfo(e) {
    if (e.detail.userInfo) {
      // 对于旧版本，直接使用 e.detail 中的用户信息
      const userProfile = {
        userInfo: e.detail.userInfo
      };

      if (this.data.loading) return;
      this.setData({ loading: true });

      try {
        const result = await app.wechatLogin(userProfile);

        if (result.success) {
          if (result.needRoleSelection) {
            wx.navigateTo({
              url: '/pages/role-select/role-select'
            });
          } else {
            this.redirectToHome();
          }
        }
      } catch (error) {
        console.error('登录失败:', error);
        wx.showToast({
          title: '登录失败',
          icon: 'error'
        });
      } finally {
        this.setData({ loading: false });
      }
    } else {
      wx.showToast({
        title: '需要授权才能使用',
        icon: 'none'
      });
    }
  }
});
