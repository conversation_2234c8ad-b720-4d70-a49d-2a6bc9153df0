<!--pages/login/login.wxml-->
<view class="login-container">
  <view class="login-header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="app-name">资管维</text>
    <text class="app-desc">设备维修管理平台</text>
  </view>

  <view class="login-content">
    <view class="welcome-text">
      <text class="welcome-title">欢迎使用</text>
      <text class="welcome-subtitle">请使用微信账号登录</text>
    </view>

    <view class="login-actions">
      <button
        class="login-btn {{loading ? 'btn-disabled' : ''}}"
        bindtap="onWechatLogin"
        disabled="{{loading}}"
      >
        <image class="wechat-icon" src="/images/wechat.png"></image>
        <text>{{loading ? '登录中...' : '微信登录'}}</text>
      </button>

      <!-- 工程师模拟登录按钮 -->
      <button
        class="login-btn engineer-login-btn {{loading ? 'btn-disabled' : ''}}"
        bindtap="onEngineerLogin"
        disabled="{{loading}}"
      >
        <image class="engineer-icon" src="/images/engineer.png"></image>
        <text>{{loading ? '登录中...' : '工程师模拟登录'}}</text>
      </button>

      <!-- 兼容旧版本的授权方式 -->
      <button
        wx:if="{{!canIUse}}"
        class="login-btn-old"
        open-type="getUserInfo"
        bindgetuserinfo="onGetUserInfo"
      >
        微信登录
      </button>
    </view>

    <view class="login-tips">
      <text class="tip-text">登录即表示同意</text>
      <text class="tip-link">《用户协议》</text>
      <text class="tip-text">和</text>
      <text class="tip-link">《隐私政策》</text>
    </view>
  </view>

  <view class="login-footer">
    <text class="footer-text">资管维 v1.0.0</text>
  </view>
</view>
