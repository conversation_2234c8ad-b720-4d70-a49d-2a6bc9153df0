/* pages/chat/chat.wxss */
.chat-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  padding: 40rpx 30rpx 20rpx;
  color: #ffffff;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
}

/* 聊天列表 */
.chat-list {
  padding: 20rpx 30rpx;
}

.chat-item {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.chat-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.chat-avatar {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.chat-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4444;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
  line-height: 1;
}

.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.chat-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-time {
  font-size: 22rpx;
  color: #999999;
  margin-left: 16rpx;
  flex-shrink: 0;
}

.chat-meta {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.order-number {
  font-size: 22rpx;
  color: #666666;
  margin-right: 16rpx;
}

.order-status {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-assigned {
  background: #cce5ff;
  color: #0066cc;
}

.status-accepted {
  background: #d4edda;
  color: #155724;
}

.status-in_progress {
  background: #e2e3f1;
  color: #6c757d;
}

.status-completed {
  background: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background: #f8d7da;
  color: #721c24;
}

.last-message {
  display: flex;
  align-items: center;
}

.sender-name {
  font-size: 22rpx;
  color: #999999;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.message-preview {
  font-size: 24rpx;
  color: #666666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-arrow {
  margin-left: 16rpx;
  color: #cccccc;
  font-size: 32rpx;
  flex-shrink: 0;
}

.arrow-icon {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 300;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #ffffff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  display: block;
  font-size: 24rpx;
  color: #cccccc;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}