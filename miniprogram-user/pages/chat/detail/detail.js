// pages/chat/detail/detail.js
const app = getApp();

Page({
  data: {
    orderId: null,
    orderInfo: {},
    messageList: [],
    inputText: '',
    sending: false,
    loading: false,
    currentUserId: null,
    currentUser: {},
    scrollTop: 0,
    scrollIntoView: ''
  },

  onLoad(options) {
    const { orderId } = options;
    if (!orderId) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      orderId,
      currentUserId: app.globalData.userInfo?.id,
      currentUser: app.globalData.userInfo || {}
    });

    this.loadOrderInfo();
    this.loadMessages();
  },

  onShow() {
    // 标记消息为已读
    this.markMessagesAsRead();
  },

  // 加载工单信息
  async loadOrderInfo() {
    try {
      const result = await app.request({
        url: `/orders/${this.data.orderId}`,
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          orderInfo: result.data
        });
        
        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: result.data.title || '工单聊天'
        });
      }
    } catch (error) {
      console.error('加载工单信息失败:', error);
    }
  },

  // 加载消息列表
  async loadMessages() {
    try {
      this.setData({ loading: true });

      const result = await app.request({
        url: '/chat/messages',
        method: 'GET',
        data: {
          order_id: this.data.orderId,
          page: 1,
          limit: 50
        }
      });

      if (result.success) {
        this.setData({
          messageList: result.data.list || []
        });
        
        // 滚动到底部
        this.scrollToBottom();
      }
    } catch (error) {
      console.error('加载消息失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 发送消息
  async onSendMessage() {
    const text = this.data.inputText.trim();
    if (!text || this.data.sending) return;

    this.setData({ sending: true });

    try {
      const result = await app.request({
        url: '/chat/send',
        method: 'POST',
        data: {
          order_id: this.data.orderId,
          message_type: 'text',
          content: text
        }
      });

      if (result.success) {
        this.setData({
          inputText: '',
          messageList: [...this.data.messageList, result.data]
        });
        
        // 滚动到底部
        this.scrollToBottom();
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      wx.showToast({
        title: '发送失败',
        icon: 'error'
      });
    } finally {
      this.setData({ sending: false });
    }
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    });
  },

  // 选择图片
  onChooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadAndSendImage(tempFilePath);
      }
    });
  },

  // 选择文件
  onChooseFile() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      success: (res) => {
        const file = res.tempFiles[0];
        this.uploadAndSendFile(file);
      }
    });
  },

  // 上传并发送图片
  async uploadAndSendImage(filePath) {
    try {
      wx.showLoading({ title: '发送中...' });
      
      // 这里应该调用上传接口
      // const uploadResult = await this.uploadFile(filePath);
      
      // 模拟上传成功
      const result = await app.request({
        url: '/chat/send',
        method: 'POST',
        data: {
          order_id: this.data.orderId,
          message_type: 'image',
          content: filePath // 实际应该是上传后的URL
        }
      });

      if (result.success) {
        this.setData({
          messageList: [...this.data.messageList, result.data]
        });
        this.scrollToBottom();
      }
    } catch (error) {
      console.error('发送图片失败:', error);
      wx.showToast({
        title: '发送失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 上传并发送文件
  async uploadAndSendFile(file) {
    try {
      wx.showLoading({ title: '发送中...' });
      
      const result = await app.request({
        url: '/chat/send',
        method: 'POST',
        data: {
          order_id: this.data.orderId,
          message_type: 'file',
          content: file.path,
          file_name: file.name
        }
      });

      if (result.success) {
        this.setData({
          messageList: [...this.data.messageList, result.data]
        });
        this.scrollToBottom();
      }
    } catch (error) {
      console.error('发送文件失败:', error);
      wx.showToast({
        title: '发送失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    wx.previewImage({
      urls: [url],
      current: url
    });
  },

  // 下载文件
  onDownloadFile(e) {
    const { url } = e.currentTarget.dataset;
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 滚动到底部
  scrollToBottom() {
    if (this.data.messageList.length > 0) {
      const lastMessage = this.data.messageList[this.data.messageList.length - 1];
      this.setData({
        scrollIntoView: `msg-${lastMessage.id}`
      });
    }
  },

  // 标记消息为已读
  async markMessagesAsRead() {
    try {
      await app.request({
        url: '/chat/mark-read',
        method: 'POST',
        data: {
          order_id: this.data.orderId
        }
      });
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '';
    
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;
    
    // 一分钟内
    if (diff < 60000) {
      return '刚刚';
    }
    
    // 一小时内
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }
    
    // 今天
    if (time.toDateString() === now.toDateString()) {
      return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }
    
    // 其他
    return time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }) + ' ' + 
           time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待分配',
      'assigned': '已分配',
      'accepted': '已接单',
      'in_progress': '处理中',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || status;
  }
});
