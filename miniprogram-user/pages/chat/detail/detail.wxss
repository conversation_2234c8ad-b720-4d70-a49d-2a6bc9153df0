/* pages/chat/detail/detail.wxss */
.chat-detail-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-info {
  flex: 1;
}

.order-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.order-number {
  display: block;
  font-size: 22rpx;
  color: #666666;
}

.order-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-assigned {
  background: #cce5ff;
  color: #0066cc;
}

.status-accepted {
  background: #d4edda;
  color: #155724;
}

.status-in_progress {
  background: #e2e3f1;
  color: #6c757d;
}

.status-completed {
  background: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background: #f8d7da;
  color: #721c24;
}

/* 消息列表 */
.message-list {
  flex: 1;
  padding: 20rpx 30rpx;
  background: #f7f7f7;
}

.message-item {
  margin-bottom: 32rpx;
}

/* 消息包装器 */
.message-wrapper {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
}

.message-left {
  justify-content: flex-start;
}

.message-right {
  justify-content: flex-end;
}

/* 头像样式 */
.message-avatar {
  width: 72rpx;
  height: 72rpx;
  flex-shrink: 0;
}

.message-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 消息主体 */
.message-main {
  max-width: calc(100% - 100rpx);
  margin: 0 16rpx;
}

.message-left .message-main {
  margin-left: 16rpx;
  margin-right: 0;
}

.message-right .message-main {
  margin-right: 16rpx;
  margin-left: 0;
}

/* 消息头部信息 */
.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 22rpx;
  color: #999999;
}

.message-left .message-header {
  justify-content: flex-start;
}

.message-right .message-header {
  justify-content: flex-end;
}

.sender-name {
  font-size: 22rpx;
  color: #666666;
  font-weight: 500;
}

.message-time {
  font-size: 20rpx;
  color: #999999;
  margin-left: 12rpx;
}

.message-right .message-time {
  margin-left: 0;
  margin-right: 12rpx;
  order: -1;
}

/* 消息气泡 */
.message-bubble {
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
  word-wrap: break-word;
  position: relative;
  display: inline-block;
  max-width: 100%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 他人消息气泡 */
.bubble-other {
  background: #ffffff;
  color: #333333;
  border-top-left-radius: 8rpx;
}

.bubble-other::before {
  content: '';
  position: absolute;
  left: -12rpx;
  top: 16rpx;
  width: 0;
  height: 0;
  border: 12rpx solid transparent;
  border-right-color: #ffffff;
  border-left: none;
}

/* 自己消息气泡 */
.bubble-self {
  background: #1976D2;
  color: #ffffff;
  border-top-right-radius: 8rpx;
}

.bubble-self::before {
  content: '';
  position: absolute;
  right: -12rpx;
  top: 16rpx;
  width: 0;
  height: 0;
  border: 12rpx solid transparent;
  border-left-color: #1976D2;
  border-right: none;
}

/* 消息内容样式 */
.message-bubble image {
  max-width: 100%;
  max-height: 400rpx;
  border-radius: 12rpx;
  display: block;
}

.file-message {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  min-width: 200rpx;
}

.bubble-other .file-message {
  background: #f0f0f0;
  border: 1rpx solid #e0e0e0;
}

.bubble-self .file-message {
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.file-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.file-name {
  font-size: 26rpx;
  flex: 1;
  word-break: break-all;
}

.bubble-self .file-name {
  color: #ffffff;
}

/* 输入区域 */
.input-section {
  background: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx;
}

.input-tools {
  display: flex;
  margin-bottom: 16rpx;
}

.tool-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: all 0.3s ease;
}

.tool-btn:active {
  background: #e0e0e0;
}

.tool-icon {
  font-size: 28rpx;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  min-height: 60rpx;
  max-height: 120rpx;
  padding: 16rpx 20rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  font-size: 26rpx;
  line-height: 1.4;
  margin-right: 16rpx;
}

.send-btn {
  width: 120rpx;
  height: 60rpx;
  background: #cccccc;
  color: #ffffff;
  border-radius: 12rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.send-btn.send-active {
  background: #1976D2;
}

.send-btn:disabled {
  background: #cccccc;
  color: #999999;
}

.send-btn.send-active:active {
  background: #1565C0;
}
