// pages/chat/chat.js
const app = getApp();

Page({
  data: {
    currentRole: 'user',
    messageList: [],
    inputText: '',
    loading: false
  },

  onLoad() {
    this.checkAuth();
    this.updateRoleInfo();
    this.loadMessages();
  },

  onShow() {
    this.updateRoleInfo();
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新角色信息
  updateRoleInfo() {
    const currentRole = app.globalData.currentRole || 'user';
    this.setData({ currentRole });
  },

  // 加载消息列表
  async loadMessages() {
    try {
      this.setData({ loading: true });

      const result = await app.request({
        url: '/messages',
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          messageList: result.data.list || []
        });
      }
    } catch (error) {
      console.error('加载消息失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 点击聊天项
  onChatTap(e) {
    const { orderId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/chat/detail/detail?orderId=${orderId}`
    });
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '';

    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;

    // 一分钟内
    if (diff < 60000) {
      return '刚刚';
    }

    // 一小时内
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }

    // 今天
    if (time.toDateString() === now.toDateString()) {
      return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }

    // 昨天
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    if (time.toDateString() === yesterday.toDateString()) {
      return '昨天 ' + time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }

    // 其他
    return time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待分配',
      'assigned': '已分配',
      'accepted': '已接单',
      'in_progress': '处理中',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || status;
  }
});