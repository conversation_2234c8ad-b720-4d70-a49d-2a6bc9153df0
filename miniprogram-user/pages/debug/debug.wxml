<!--pages/debug/debug.wxml-->
<view class="container">
  <view class="section">
    <view class="section-title">🔧 环境配置</view>
    
    <view class="env-info">
      <view class="info-item">
        <text class="label">当前模式:</text>
        <text class="value {{environmentInfo.isDevelopment ? 'mock' : 'real'}}">
          {{environmentInfo.isDevelopment ? 'Mock模式' : '真实API'}}
        </text>
      </view>
      
      <view class="info-item">
        <text class="label">调用方式:</text>
        <text class="value">
          {{environmentInfo.useCloudContainer ? '云托管容器' : 'HTTP请求'}}
        </text>
      </view>
      
      <view class="info-item">
        <text class="label">API地址:</text>
        <text class="value small">{{environmentInfo.baseUrl}}</text>
      </view>
      
      <view class="info-item">
        <text class="label">云环境:</text>
        <text class="value small">{{environmentInfo.cloudConfig.env}}</text>
      </view>
    </view>
    
    <view class="button-group">
      <button class="btn {{environmentInfo.isDevelopment ? 'active' : ''}}" 
              bindtap="switchToMock">Mock模式</button>
      <button class="btn {{!environmentInfo.isDevelopment ? 'active' : ''}}" 
              bindtap="switchToReal">真实API</button>
    </view>
    
    <view class="button-group">
      <button class="btn {{environmentInfo.useCloudContainer ? 'active' : ''}}" 
              bindtap="switchToCloud">云托管</button>
      <button class="btn {{!environmentInfo.useCloudContainer ? 'active' : ''}}" 
              bindtap="switchToHttp">HTTP</button>
    </view>
  </view>

  <view class="section">
    <view class="section-title">🧪 API测试</view>
    
    <view class="button-group">
      <button class="test-btn" bindtap="testHealth">健康检查</button>
      <button class="test-btn" bindtap="testLogin">登录接口</button>
    </view>

    <view class="button-group">
      <button class="test-btn" bindtap="testEquipment">设备列表</button>
      <button class="test-btn" bindtap="testOrders">工单列表</button>
    </view>

    <view class="button-group">
      <button class="test-btn" bindtap="testHttpDirect">直接HTTP测试</button>
      <button class="test-btn" bindtap="testCloudDirect">直接云托管测试</button>
    </view>

    <view class="button-group">
      <button class="test-btn" bindtap="testCloudLogin">云托管登录测试</button>
    </view>
    
    <button class="clear-btn" bindtap="clearResults">清空结果</button>
  </view>

  <view class="section">
    <view class="section-title">📋 测试结果</view>
    
    <view wx:if="{{testResults.length === 0}}" class="empty">
      暂无测试结果
    </view>
    
    <view wx:for="{{testResults}}" wx:key="id" class="result-item">
      <view class="result-header">
        <text class="test-name">{{item.testName}}</text>
        <text class="status {{item.status === '成功' ? 'success' : 'error'}}">
          {{item.status}}
        </text>
        <text class="timestamp">{{item.timestamp}}</text>
      </view>
      
      <view class="result-data" bindtap="copyResult" data-data="{{item.data}}">
        <text>{{item.data}}</text>
      </view>
    </view>
  </view>
</view>
