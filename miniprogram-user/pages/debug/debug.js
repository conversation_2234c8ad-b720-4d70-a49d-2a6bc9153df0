// pages/debug/debug.js
const app = getApp();

Page({
  data: {
    environmentInfo: {},
    testResults: []
  },

  onLoad() {
    this.updateEnvironmentInfo();
  },

  // 更新环境信息
  updateEnvironmentInfo() {
    const envInfo = app.getEnvironmentInfo();
    this.setData({
      environmentInfo: envInfo
    });
  },

  // 切换到Mock模式
  switchToMock() {
    app.switchToMockMode();
    this.updateEnvironmentInfo();
  },

  // 切换到真实API模式
  switchToReal() {
    app.switchToRealMode();
    this.updateEnvironmentInfo();
  },

  // 切换到云托管调用
  switchToCloud() {
    app.switchToCloudContainer();
    this.updateEnvironmentInfo();
  },

  // 切换到HTTP调用
  switchToHttp() {
    app.switchToHttp();
    this.updateEnvironmentInfo();
  },

  // 测试健康检查
  async testHealth() {
    try {
      wx.showLoading({ title: '测试中...' });
      
      const result = await app.request({
        url: '/health',
        method: 'GET',
        needAuth: false
      });
      
      this.addTestResult('健康检查', '成功', result);
      
    } catch (error) {
      this.addTestResult('健康检查', '失败', error.message);
    } finally {
      wx.hideLoading();
    }
  },

  // 测试登录接口
  async testLogin() {
    try {
      wx.showLoading({ title: '测试中...' });
      
      const result = await app.request({
        url: '/auth/wechat-login',
        method: 'POST',
        data: {
          code: 'test_code',
          userInfo: {
            nickName: '测试用户',
            avatarUrl: ''
          }
        },
        needAuth: false
      });
      
      this.addTestResult('微信登录', '成功', result);
      
    } catch (error) {
      this.addTestResult('微信登录', '失败', error.message);
    } finally {
      wx.hideLoading();
    }
  },

  // 测试设备列表
  async testEquipment() {
    try {
      wx.showLoading({ title: '测试中...' });
      
      const result = await app.request({
        url: '/equipment',
        method: 'GET',
        needAuth: false
      });
      
      this.addTestResult('设备列表', '成功', result);
      
    } catch (error) {
      this.addTestResult('设备列表', '失败', error.message);
    } finally {
      wx.hideLoading();
    }
  },

  // 测试工单列表
  async testOrders() {
    try {
      wx.showLoading({ title: '测试中...' });
      
      const result = await app.request({
        url: '/orders',
        method: 'GET',
        needAuth: false
      });
      
      this.addTestResult('工单列表', '成功', result);
      
    } catch (error) {
      this.addTestResult('工单列表', '失败', error.message);
    } finally {
      wx.hideLoading();
    }
  },

  // 添加测试结果
  addTestResult(testName, status, data) {
    const testResults = this.data.testResults;
    testResults.unshift({
      id: Date.now(),
      testName,
      status,
      data: typeof data === 'object' ? JSON.stringify(data, null, 2) : data,
      timestamp: new Date().toLocaleString()
    });
    
    // 只保留最近10条结果
    if (testResults.length > 10) {
      testResults.splice(10);
    }
    
    this.setData({
      testResults
    });
  },

  // 清空测试结果
  clearResults() {
    this.setData({
      testResults: []
    });
  },

  // 复制结果
  copyResult(e) {
    const { data } = e.currentTarget.dataset;
    wx.setClipboardData({
      data: data,
      success() {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  }
});
