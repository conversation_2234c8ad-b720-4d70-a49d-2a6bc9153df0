/* pages/debug/debug.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  border-left: 6rpx solid #007aff;
  padding-left: 20rpx;
}

/* 环境信息 */
.env-info {
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.value.small {
  font-size: 24rpx;
  color: #999;
}

.value.mock {
  color: #ff9500;
  font-weight: bold;
}

.value.real {
  color: #34c759;
  font-weight: bold;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e5e5;
  background-color: white;
  color: #666;
}

.btn.active {
  background-color: #007aff;
  color: white;
  border-color: #007aff;
}

.test-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  background-color: #34c759;
  color: white;
  border: none;
}

.clear-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  background-color: #ff3b30;
  color: white;
  border: none;
  margin-top: 20rpx;
}

/* 测试结果 */
.empty {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 60rpx 0;
}

.result-item {
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.result-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 2rpx solid #e5e5e5;
}

.test-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
}

.status.success {
  background-color: #34c759;
  color: white;
}

.status.error {
  background-color: #ff3b30;
  color: white;
}

.timestamp {
  font-size: 22rpx;
  color: #999;
}

.result-data {
  padding: 30rpx;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  word-break: break-all;
  background-color: #f8f9fa;
  font-family: 'Courier New', monospace;
  max-height: 400rpx;
  overflow-y: auto;
}

.result-data:active {
  background-color: #e9ecef;
}
