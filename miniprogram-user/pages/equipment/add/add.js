// pages/equipment/add/add.js
const app = getApp();

Page({
  data: {
    formData: {
      name: '',
      model: '',
      serialNumber: '',
      manufacturer: '',
      purchaseDate: '',
      warrantyExpiry: '',
      location: '',
      department: '',
      researchGroup: '',
      description: '',
      specifications: '',
      price: '',
      supplier: '',
      category: 'instrument'
    },
    categories: [
      { value: 'instrument', label: '仪器设备' },
      { value: 'computer', label: '计算机设备' },
      { value: 'furniture', label: '办公家具' },
      { value: 'other', label: '其他设备' }
    ],
    loading: false,
    currentRole: 'user',
    canAdd: false
  },

  onLoad() {
    this.checkAuth();
    this.updateRoleInfo();
  },

  onShow() {
    this.updateRoleInfo();
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新角色信息
  updateRoleInfo() {
    const currentRole = app.globalData.currentRole || 'user';
    const canAdd = ['pi', 'admin', 'super_admin'].includes(currentRole);

    this.setData({
      currentRole,
      canAdd
    });

    if (!canAdd) {
      wx.showModal({
        title: '权限不足',
        content: '您没有添加设备的权限',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 分类选择
  onCategoryChange(e) {
    const index = e.detail.value;
    this.setData({
      'formData.category': this.data.categories[index].value
    });
  },

  // 日期选择
  onDateChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;
    const required = ['name', 'model', 'serialNumber', 'location'];

    for (let field of required) {
      if (!formData[field] || !formData[field].trim()) {
        const fieldNames = {
          name: '设备名称',
          model: '设备型号',
          serialNumber: '序列号',
          location: '存放位置'
        };

        wx.showToast({
          title: `请填写${fieldNames[field]}`,
          icon: 'none'
        });
        return false;
      }
    }

    // 验证日期格式
    if (formData.purchaseDate && formData.warrantyExpiry) {
      if (new Date(formData.purchaseDate) >= new Date(formData.warrantyExpiry)) {
        wx.showToast({
          title: '保修期应晚于购买日期',
          icon: 'none'
        });
        return false;
      }
    }

    return true;
  },

  // 提交表单
  async onSubmit() {
    if (!this.validateForm()) return;
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const result = await app.request({
        url: '/equipment',
        method: 'POST',
        data: this.data.formData
      });

      if (result.success) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('添加设备失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 重置表单
  onReset() {
    wx.showModal({
      title: '确认重置',
      content: '确定要清空所有输入内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            formData: {
              name: '',
              model: '',
              serialNumber: '',
              manufacturer: '',
              purchaseDate: '',
              warrantyExpiry: '',
              location: '',
              department: '',
              researchGroup: '',
              description: '',
              specifications: '',
              price: '',
              supplier: '',
              category: 'instrument'
            }
          });
        }
      }
    });
  }
});