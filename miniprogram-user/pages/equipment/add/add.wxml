<!--pages/equipment/add/add.wxml-->
<view class="add-equipment-container">
  <view class="page-header">
    <text class="page-title">添加设备</text>
    <text class="page-subtitle">请填写设备的详细信息</text>
  </view>

  <form class="equipment-form">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <view class="form-item required">
        <text class="label">设备名称</text>
        <input
          class="input"
          placeholder="请输入设备名称"
          value="{{formData.name}}"
          data-field="name"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item required">
        <text class="label">设备型号</text>
        <input
          class="input"
          placeholder="请输入设备型号"
          value="{{formData.model}}"
          data-field="model"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item required">
        <text class="label">序列号</text>
        <input
          class="input"
          placeholder="请输入设备序列号"
          value="{{formData.serialNumber}}"
          data-field="serialNumber"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="label">制造商</text>
        <input
          class="input"
          placeholder="请输入制造商"
          value="{{formData.manufacturer}}"
          data-field="manufacturer"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="label">设备分类</text>
        <picker
          mode="selector"
          range="{{categories}}"
          range-key="label"
          bindchange="onCategoryChange"
        >
          <view class="picker">
            {{categories[0].label}}
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 位置信息 -->
    <view class="form-section">
      <view class="section-title">位置信息</view>

      <view class="form-item required">
        <text class="label">存放位置</text>
        <input
          class="input"
          placeholder="如：实验室A-101"
          value="{{formData.location}}"
          data-field="location"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="label">所属部门</text>
        <input
          class="input"
          placeholder="请输入所属部门"
          value="{{formData.department}}"
          data-field="department"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="label">课题组</text>
        <input
          class="input"
          placeholder="请输入课题组名称"
          value="{{formData.researchGroup}}"
          data-field="researchGroup"
          bindinput="onInputChange"
        />
      </view>
    </view>

    <!-- 采购信息 -->
    <view class="form-section">
      <view class="section-title">采购信息</view>

      <view class="form-item">
        <text class="label">购买日期</text>
        <picker
          mode="date"
          value="{{formData.purchaseDate}}"
          data-field="purchaseDate"
          bindchange="onDateChange"
        >
          <view class="picker">
            {{formData.purchaseDate || '请选择购买日期'}}
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">保修期至</text>
        <picker
          mode="date"
          value="{{formData.warrantyExpiry}}"
          data-field="warrantyExpiry"
          bindchange="onDateChange"
        >
          <view class="picker">
            {{formData.warrantyExpiry || '请选择保修期'}}
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">采购价格</text>
        <input
          class="input"
          type="digit"
          placeholder="请输入采购价格（元）"
          value="{{formData.price}}"
          data-field="price"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="label">供应商</text>
        <input
          class="input"
          placeholder="请输入供应商名称"
          value="{{formData.supplier}}"
          data-field="supplier"
          bindinput="onInputChange"
        />
      </view>
    </view>

    <!-- 详细信息 -->
    <view class="form-section">
      <view class="section-title">详细信息</view>

      <view class="form-item">
        <text class="label">设备描述</text>
        <textarea
          class="textarea"
          placeholder="请输入设备描述和用途"
          value="{{formData.description}}"
          data-field="description"
          bindinput="onInputChange"
          maxlength="500"
        ></textarea>
      </view>

      <view class="form-item">
        <text class="label">技术规格</text>
        <textarea
          class="textarea"
          placeholder="请输入设备的技术规格参数"
          value="{{formData.specifications}}"
          data-field="specifications"
          bindinput="onInputChange"
          maxlength="500"
        ></textarea>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button
        class="btn btn-outline"
        bindtap="onReset"
        disabled="{{loading}}"
      >
        重置
      </button>

      <button
        class="btn btn-primary {{loading ? 'btn-disabled' : ''}}"
        bindtap="onSubmit"
        disabled="{{loading}}"
      >
        {{loading ? '提交中...' : '提交'}}
      </button>
    </view>
  </form>
</view>