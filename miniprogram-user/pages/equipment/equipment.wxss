/* pages/equipment/equipment.wxss */
.equipment-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 角色切换器 */
.role-switcher-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #1976D2;
  padding: 20rpx 30rpx;
}

/* 统计区域 */
.stats-section {
  margin: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.stats-grid {
  display: flex;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 20rpx;
  border-right: 1rpx solid #f0f0f0;
}

.stat-item:last-child {
  border-right: none;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1976D2;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

/* 搜索区域 */
.search-section {
  margin: 0 30rpx 20rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  margin-right: 20rpx;
}

.search-input {
  width: 100%;
  height: 60rpx;
  padding: 0 20rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.clear-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
  background: #cccccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #ffffff;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-left: 16rpx;
}

/* 筛选下拉选择器 */
.filter-picker {
  flex: 0 0 auto;
  min-width: 120rpx;
  max-width: 240rpx;
}

.filter-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60rpx;
  padding: 0 16rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  border: 2rpx solid #d0d0d0;
  white-space: nowrap;
  min-width: 120rpx;
  box-sizing: border-box;
}

.filter-text {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.filter-arrow {
  font-size: 20rpx;
  color: #666666;
  margin-left: 8rpx;
  flex-shrink: 0;
}

/* 扫描按钮 */
.scan-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 2rpx solid #d0d0d0;
  box-sizing: border-box;
}

.scan-btn:active {
  background: #e0e0e0;
}

.scan-icon {
  font-size: 28rpx;
}



/* 设备列表 */
.equipment-list {
  margin: 0 30rpx;
}

.equipment-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.equipment-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.equipment-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.equipment-info {
  flex: 1;
}

.equipment-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.equipment-code {
  display: block;
  font-size: 26rpx;
  color: #666666;
}

.equipment-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
}

.status-normal {
  background: #E8F5E8;
  color: #388E3C;
}

.status-maintenance {
  background: #FFF3E0;
  color: #F57C00;
}

.status-fault {
  background: #FFEBEE;
  color: #D32F2F;
}

.status-retired {
  background: #F5F5F5;
  color: #999999;
}

.equipment-details {
  margin-bottom: 16rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 26rpx;
  color: #999999;
  min-width: 80rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #666666;
  flex: 1;
}

.equipment-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.update-time {
  font-size: 22rpx;
  color: #999999;
}

.equipment-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.action-btn.edit {
  background: #E3F2FD;
  color: #1976D2;
}

.action-btn.delete {
  background: #FFEBEE;
  color: #D32F2F;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: #ffffff;
  margin: 0 30rpx;
  border-radius: 16rpx;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 40rpx;
}

/* 加载状态 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666666;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #999999;
}

/* 悬浮按钮 */
.fab {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  background: #1976D2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
}

.fab:hover {
  transform: scale(1.1);
}

.fab-icon {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: 300;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .equipment-container {
    padding-bottom: 100rpx;
  }
  
  .stats-section,
  .search-section,
  .equipment-list {
    margin-left: 20rpx;
    margin-right: 20rpx;
  }
  
  .equipment-item {
    padding: 24rpx;
  }
  
  .fab {
    right: 20rpx;
    bottom: 100rpx;
    width: 80rpx;
    height: 80rpx;
  }
  
  .fab-icon {
    font-size: 40rpx;
  }
}
