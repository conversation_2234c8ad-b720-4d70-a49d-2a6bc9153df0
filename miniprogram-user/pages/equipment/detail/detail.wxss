/* pages/equipment/detail/detail.wxss */
.equipment-detail-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 设备头部 */
.equipment-header {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  padding: 40rpx 30rpx;
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.equipment-info {
  flex: 1;
}

.equipment-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.equipment-model {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 16rpx;
}

.status-badge {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-normal {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.status-maintenance {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
}

.status-fault {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}

.status-retired {
  background: rgba(158, 158, 158, 0.2);
  color: #9E9E9E;
}

.equipment-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
}

.action-btn.primary {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn.secondary {
  background: rgba(255, 152, 0, 0.2);
  color: #ffffff;
  border: 1rpx solid rgba(255, 152, 0, 0.3);
}

.action-btn.outline {
  background: transparent;
  color: #ffffff;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
}

/* 信息区块 */
.info-section {
  background: #ffffff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-btn {
  padding: 12rpx 24rpx;
  background: #1976D2;
  color: #ffffff;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
}

/* 信息网格 */
.info-grid {
  padding: 20rpx 30rpx 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666666;
  width: 200rpx;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.copy-icon {
  margin-left: 16rpx;
  font-size: 24rpx;
  opacity: 0.6;
}

/* 描述信息 */
.description-item {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.description-item:last-child {
  border-bottom: none;
}

.desc-label {
  display: block;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.desc-content {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}

/* 二维码样式 */
.qr-code-container {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-canvas {
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  background: #ffffff;
}

.qr-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.qr-btn {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  background: #1976D2;
  color: #ffffff;
}

.qr-btn.outline {
  background: transparent;
  color: #1976D2;
  border: 1rpx solid #1976D2;
}

.qr-info {
  text-align: center;
}

.qr-desc {
  font-size: 24rpx;
  color: #999999;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-btn-large {
  width: 100%;
  height: 88rpx;
  background: #1976D2;
  color: #ffffff;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}

/* 维修历史弹窗 */
.history-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-content {
  width: 90%;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #666666;
  border-radius: 50%;
  background: #f5f5f5;
}

.history-list {
  max-height: 60vh;
  overflow-y: auto;
}

.empty-history {
  padding: 80rpx;
  text-align: center;
  color: #999999;
  font-size: 28rpx;
}

.order-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.order-item:last-child {
  border-bottom: none;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.order-title {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.order-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.order-number,
.order-date {
  font-size: 24rpx;
  color: #666666;
}