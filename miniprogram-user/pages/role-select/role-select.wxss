/* pages/role-select/role-select.wxss */
.role-select-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 42rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

.role-list {
  margin-bottom: 60rpx;
}

.role-item {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.role-item.selected {
  border: 2rpx solid #1976D2;
  background: #f3f8ff;
}

.role-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.role-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.role-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.role-icon image {
  width: 100%;
  height: 100%;
}

.role-content {
  flex: 1;
  min-width: 0;
}

.role-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.role-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
}

.role-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.role-status.selected {
  background: #1976D2;
  color: #ffffff;
}

.role-status.disabled {
  background: #f5f5f5;
  color: #999999;
}

.role-description {
  display: block;
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.role-department {
  font-size: 24rpx;
  color: #999999;
}

.role-indicator {
  margin-left: 20rpx;
  flex-shrink: 0;
}

.radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d0d0d0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio.checked {
  border-color: #1976D2;
  background: #1976D2;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  background: #ffffff;
  border-radius: 50%;
}

.actions {
  margin-bottom: 40rpx;
}

.tips {
  text-align: center;
  padding: 30rpx;
  background: #fff9e6;
  border-radius: 12rpx;
  border-left: 4rpx solid #ffa726;
}

.tip-text {
  font-size: 26rpx;
  color: #f57c00;
  line-height: 1.5;
}

/* 动画效果 */
.role-item {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .role-select-container {
    padding: 30rpx 20rpx;
  }
  
  .role-item {
    padding: 24rpx;
  }
  
  .role-icon {
    width: 60rpx;
    height: 60rpx;
    margin-right: 20rpx;
  }
  
  .role-name {
    font-size: 28rpx;
  }
}
