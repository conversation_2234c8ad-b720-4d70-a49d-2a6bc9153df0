<!--pages/role-select/role-select.wxml-->
<view class="role-select-container">
  <view class="header">
    <text class="title">选择身份</text>
    <text class="subtitle">您拥有多个身份，请选择要使用的身份</text>
  </view>

  <view class="role-list">
    <view 
      class="role-item {{selectedRole === item.role ? 'selected' : ''}} {{!item.isActive ? 'disabled' : ''}}"
      wx:for="{{roles}}"
      wx:key="role"
      data-role="{{item.role}}"
      bindtap="onSelectRole"
    >
      <view class="role-icon">
        <image src="{{item.icon}}" mode="aspectFit"></image>
      </view>
      
      <view class="role-content">
        <view class="role-header">
          <text class="role-name">{{item.name}}</text>
          <view wx:if="{{!item.isActive}}" class="role-status disabled">已禁用</view>
          <view wx:if="{{selectedRole === item.role}}" class="role-status selected">已选择</view>
        </view>
        
        <text class="role-description">{{item.description}}</text>
        
        <view wx:if="{{item.department}}" class="role-department">
          <text>部门：{{item.department}}</text>
        </view>
      </view>
      
      <view class="role-indicator">
        <view class="radio {{selectedRole === item.role ? 'checked' : ''}}">
          <view wx:if="{{selectedRole === item.role}}" class="radio-inner"></view>
        </view>
      </view>
    </view>
  </view>

  <view class="actions">
    <button 
      class="btn btn-primary btn-block {{loading ? 'btn-disabled' : ''}}"
      bindtap="onConfirmRole"
      disabled="{{loading || !selectedRole}}"
    >
      {{loading ? '切换中...' : '确认选择'}}
    </button>
    
    <button 
      class="btn btn-outline btn-block mt-10"
      bindtap="onSkipSelection"
    >
      暂时跳过
    </button>
  </view>

  <view class="tips">
    <text class="tip-text">💡 您可以在个人中心随时切换身份</text>
  </view>
</view>
