<!--pages/orders/orders.wxml-->
<view class="orders-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">工单管理</text>
      <role-switcher mode="compact" custom-class="header-role-switcher" bind:rolechange="onRoleChange"></role-switcher>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs-container">
    <scroll-view class="tabs-scroll" scroll-x="true">
      <view class="tabs">
        <view
          class="tab-item {{activeTab === item.key ? 'active' : ''}}"
          wx:for="{{tabs}}"
          wx:key="key"
          data-tab="{{item.key}}"
          bindtap="onTabChange"
        >
          <text class="tab-name">{{item.name}}</text>
          <text wx:if="{{item.count > 0}}" class="tab-count">{{item.count}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 工单列表 -->
  <view class="orders-content">
    <!-- 加载状态 -->
    <view wx:if="{{loading && orderList.length === 0}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空数据提示 -->
    <view wx:elif="{{!loading && orderList.length === 0}}" class="empty-container">
      <image class="empty-image" src="/images/empty-orders.png" mode="aspectFit"></image>
      <text class="empty-title">暂无工单</text>
      <text class="empty-subtitle">{{getEmptyText()}}</text>
    </view>

    <!-- 工单列表 -->
    <view wx:else class="order-list">
      <view
        class="order-item"
        wx:for="{{orderList}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onOrderTap"
      >
        <view class="order-header">
          <view class="order-title-row">
            <text class="order-title">{{item.title}}</text>
            <view class="order-status {{getStatusClass(item.status)}}">
              {{getStatusText(item.status)}}
            </view>
          </view>
          <view class="order-meta">
            <text class="order-number">{{item.orderNumber}}</text>
            <view class="order-priority {{getPriorityClass(item.priority)}}">
              {{getPriorityText(item.priority)}}
            </view>
          </view>
        </view>

        <view class="order-content">
          <view wx:if="{{item.equipment}}" class="equipment-info">
            <text class="equipment-label">设备：</text>
            <text class="equipment-name">{{item.equipment.name}} ({{item.equipment.model}})</text>
          </view>

          <view class="order-info-row">
            <view class="info-item">
              <text class="info-label">创建人：</text>
              <text class="info-value">{{item.creator.username}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">创建时间：</text>
              <text class="info-value">{{formatTime(item.createdAt)}}</text>
            </view>
          </view>

          <view wx:if="{{item.assignee}}" class="order-info-row">
            <view class="info-item">
              <text class="info-label">处理人：</text>
              <text class="info-value">{{item.assignee.username}}</text>
            </view>
          </view>
        </view>

        <!-- 快捷操作 -->
        <view wx:if="{{showQuickActions(item)}}" class="order-actions">
          <button wx:if="{{canAccept(item)}}"
                  class="action-btn primary"
                  data-id="{{item.id}}"
                  catchtap="onAcceptOrder">
            接受
          </button>
          <button wx:if="{{canComplete(item)}}"
                  class="action-btn success"
                  data-id="{{item.id}}"
                  catchtap="onCompleteOrder">
            完成
          </button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{hasMore && orderList.length > 0}}" class="load-more">
      <button class="load-more-btn" bindtap="onLoadMore" disabled="{{loading}}">
        {{loading ? '加载中...' : '加载更多'}}
      </button>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!hasMore && orderList.length > 0}}" class="no-more">
      <text>没有更多数据了</text>
    </view>
  </view>

  <!-- 悬浮创建按钮 -->
  <view wx:if="{{currentRole === 'user' || currentRole === 'pi'}}"
        class="fab"
        bindtap="onCreateOrder">
    <text class="fab-icon">+</text>
  </view>
</view>