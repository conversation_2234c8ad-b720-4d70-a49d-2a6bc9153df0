// pages/orders/detail/detail.js
const app = getApp();

Page({
  data: {
    orderId: null,
    order: null,
    loading: true,
    currentRole: 'user',
    canEdit: false,
    canProcess: false,
    statusHistory: [],
    showStatusHistory: false,
    actionLoading: false
  },

  onLoad(options) {
    const { id } = options;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => wx.navigateBack(), 1500);
      return;
    }

    this.setData({ orderId: id });
    this.checkAuth();
    this.updateRoleInfo();
    this.loadOrderDetail();
  },

  onShow() {
    this.updateRoleInfo();
  },

  onPullDownRefresh() {
    this.loadOrderDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新角色信息
  updateRoleInfo() {
    const currentRole = app.globalData.currentRole || 'user';
    const canEdit = ['admin', 'super_admin'].includes(currentRole);
    const canProcess = ['engineer', 'admin', 'super_admin'].includes(currentRole);

    this.setData({
      currentRole,
      canEdit,
      canProcess
    });
  },

  // 加载工单详情
  async loadOrderDetail() {
    try {
      this.setData({ loading: true });

      const result = await app.request({
        url: `/orders/${this.data.orderId}`,
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          order: result.data
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('加载工单详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载状态历史
  async loadStatusHistory() {
    try {
      const result = await app.request({
        url: `/orders/${this.data.orderId}/history`,
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          statusHistory: result.data.list || [],
          showStatusHistory: true
        });
      }
    } catch (error) {
      console.error('加载状态历史失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 接受工单
  async onAcceptOrder() {
    if (!this.data.canProcess) {
      wx.showToast({
        title: '暂无权限',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认接受',
      content: '确定要接受这个工单吗？',
      success: async (res) => {
        if (res.confirm) {
          await this.updateOrderStatus('accepted');
        }
      }
    });
  },

  // 开始处理
  async onStartProcess() {
    if (!this.data.canProcess) {
      wx.showToast({
        title: '暂无权限',
        icon: 'none'
      });
      return;
    }

    await this.updateOrderStatus('in_progress');
  },

  // 完成工单
  async onCompleteOrder() {
    if (!this.data.canProcess) {
      wx.showToast({
        title: '暂无权限',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认完成',
      content: '确定要标记工单为已完成吗？',
      success: async (res) => {
        if (res.confirm) {
          await this.updateOrderStatus('completed');
        }
      }
    });
  },

  // 更新工单状态
  async updateOrderStatus(status) {
    if (this.data.actionLoading) return;

    this.setData({ actionLoading: true });

    try {
      const result = await app.request({
        url: `/orders/${this.data.orderId}/status`,
        method: 'PUT',
        data: { status }
      });

      if (result.success) {
        wx.showToast({
          title: '状态更新成功',
          icon: 'success'
        });

        // 重新加载工单详情
        this.loadOrderDetail();
      }
    } catch (error) {
      console.error('更新状态失败:', error);
      wx.showToast({
        title: '更新失败',
        icon: 'error'
      });
    } finally {
      this.setData({ actionLoading: false });
    }
  },

  // 查看状态历史
  onViewHistory() {
    this.loadStatusHistory();
  },

  // 隐藏状态历史
  onHideHistory() {
    this.setData({ showStatusHistory: false });
  },

  // 编辑工单
  onEditOrder() {
    if (!this.data.canEdit) {
      wx.showToast({
        title: '暂无权限',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/orders/edit/edit?id=${this.data.orderId}`
    });
  },

  // 查看设备详情
  onViewEquipment() {
    if (this.data.order && this.data.order.equipment) {
      wx.navigateTo({
        url: `/pages/equipment/detail/detail?id=${this.data.order.equipment.id}`
      });
    }
  },

  // 联系创建者
  onContactCreator() {
    const { order } = this.data;
    if (order && order.contactPhone) {
      wx.makePhoneCall({
        phoneNumber: order.contactPhone
      });
    }
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待处理',
      'accepted': '已接受',
      'in_progress': '处理中',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知';
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      'low': '低',
      'medium': '中',
      'high': '高',
      'urgent': '紧急'
    };
    return priorityMap[priority] || '未知';
  },

  // 获取状态样式
  getStatusClass(status) {
    const classMap = {
      'pending': 'status-pending',
      'accepted': 'status-accepted',
      'in_progress': 'status-processing',
      'completed': 'status-completed',
      'cancelled': 'status-cancelled'
    };
    return classMap[status] || '';
  },

  // 获取优先级样式
  getPriorityClass(priority) {
    const classMap = {
      'low': 'priority-low',
      'medium': 'priority-medium',
      'high': 'priority-high',
      'urgent': 'priority-urgent'
    };
    return classMap[priority] || '';
  },

  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    const { order } = this.data;
    wx.previewImage({
      current: url,
      urls: order.images || [url]
    });
  },

  // 复制工单号
  onCopyOrderNumber() {
    const { order } = this.data;
    if (order && order.orderNumber) {
      wx.setClipboardData({
        data: order.orderNumber,
        success: () => {
          wx.showToast({
            title: '已复制',
            icon: 'success'
          });
        }
      });
    }
  }
});