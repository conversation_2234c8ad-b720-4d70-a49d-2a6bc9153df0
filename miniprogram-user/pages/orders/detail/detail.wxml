<!--pages/orders/detail/detail.wxml-->
<view class="order-detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 工单详情 -->
  <view wx:else class="detail-content">
    <!-- 工单头部信息 -->
    <view class="order-header">
      <view class="order-info">
        <view class="order-title-row">
          <text class="order-title">{{order.title}}</text>
          <view class="status-badge {{getStatusClass(order.status)}}">
            {{getStatusText(order.status)}}
          </view>
        </view>
        <view class="order-meta">
          <text class="order-number" bindtap="onCopyOrderNumber">
            工单号：{{order.orderNumber}} 📋
          </text>
          <view class="priority-badge {{getPriorityClass(order.priority)}}">
            {{getPriorityText(order.priority)}}优先级
          </view>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">基本信息</view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">故障描述</text>
          <text class="info-value description">{{order.description}}</text>
        </view>

        <view class="info-item">
          <text class="info-label">故障类型</text>
          <text class="info-value">{{order.faultType || '未分类'}}</text>
        </view>

        <view class="info-item">
          <text class="info-label">紧急程度</text>
          <text class="info-value">{{order.urgentLevel || '正常'}}</text>
        </view>

        <view class="info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{order.createdAt}}</text>
        </view>

        <view wx:if="{{order.expectedDate}}" class="info-item">
          <text class="info-label">期望完成时间</text>
          <text class="info-value">{{order.expectedDate}}</text>
        </view>
      </view>
    </view>

    <!-- 设备信息 -->
    <view wx:if="{{order.equipment}}" class="info-section">
      <view class="section-title">相关设备</view>
      <view class="equipment-card" bindtap="onViewEquipment">
        <view class="equipment-info">
          <text class="equipment-name">{{order.equipment.name}}</text>
          <text class="equipment-model">{{order.equipment.model}}</text>
          <text class="equipment-location">位置：{{order.equipment.location}}</text>
        </view>
        <text class="view-arrow arrow-icon">›</text>
      </view>
    </view>

    <!-- 联系信息 -->
    <view class="info-section">
      <view class="section-title">联系信息</view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">创建人</text>
          <text class="info-value">{{order.creator.username || '未知'}}</text>
        </view>

        <view wx:if="{{order.contactPhone}}" class="info-item">
          <text class="info-label">联系电话</text>
          <view class="info-value contact-phone" bindtap="onContactCreator">
            <text>{{order.contactPhone}}</text>
            <text class="phone-icon">📞</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 故障图片 -->
    <view wx:if="{{order.images && order.images.length > 0}}" class="info-section">
      <view class="section-title">故障图片</view>
      <view class="image-gallery">
        <image
          class="fault-image"
          wx:for="{{order.images}}"
          wx:key="index"
          src="{{item}}"
          mode="aspectFill"
          data-url="{{item}}"
          bindtap="onPreviewImage"
        ></image>
      </view>
    </view>

    <!-- 处理历史 -->
    <view class="info-section">
      <view class="section-header">
        <text class="section-title">处理历史</text>
        <button class="history-btn" bindtap="onViewHistory">查看详情</button>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view wx:if="{{canProcess}}" class="action-section">
      <view class="action-buttons">
        <button wx:if="{{order.status === 'pending'}}"
                class="action-btn primary"
                bindtap="onAcceptOrder"
                disabled="{{actionLoading}}">
          接受工单
        </button>

        <button wx:if="{{order.status === 'accepted'}}"
                class="action-btn primary"
                bindtap="onStartProcess"
                disabled="{{actionLoading}}">
          开始处理
        </button>

        <button wx:if="{{order.status === 'in_progress'}}"
                class="action-btn success"
                bindtap="onCompleteOrder"
                disabled="{{actionLoading}}">
          完成工单
        </button>

        <button wx:if="{{canEdit}}"
                class="action-btn outline"
                bindtap="onEditOrder"
                disabled="{{actionLoading}}">
          编辑工单
        </button>
      </view>
    </view>
  </view>

  <!-- 状态历史弹窗 -->
  <view wx:if="{{showStatusHistory}}" class="history-modal" bindtap="onHideHistory">
    <view class="history-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">处理历史</text>
        <view class="close-btn" bindtap="onHideHistory">×</view>
      </view>

      <view class="history-list">
        <view wx:if="{{statusHistory.length === 0}}" class="empty-history">
          <text>暂无处理记录</text>
        </view>

        <block wx:else>
          <view class="history-item"
                wx:for="{{statusHistory}}"
                wx:key="id">
            <view class="history-time">{{item.createdAt}}</view>
            <view class="history-action">{{item.action}}</view>
            <view wx:if="{{item.operator}}" class="history-operator">
              操作人：{{item.operator}}
            </view>
            <view wx:if="{{item.remark}}" class="history-remark">
              备注：{{item.remark}}
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>
</view>