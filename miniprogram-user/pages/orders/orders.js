// pages/orders/orders.js
const app = getApp();

Page({
  data: {
    orderList: [],
    loading: false,
    refreshing: false,
    hasMore: true,
    currentPage: 1,
    pageSize: 10,
    activeTab: 'all',
    currentRole: 'user',
    canManage: false,
    stats: {
      total: 0,
      pending: 0,
      inProgress: 0,
      completed: 0
    },
    tabs: [
      { key: 'all', name: '全部', count: 0 },
      { key: 'pending', name: '待处理', count: 0 },
      { key: 'in_progress', name: '进行中', count: 0 },
      { key: 'completed', name: '已完成', count: 0 }
    ]
  },

  onLoad() {
    this.checkAuth();
    this.updateRoleInfo();
    this.loadOrderList();
  },

  onShow() {
    this.updateRoleInfo();
    if (this.data.orderList.length > 0) {
      this.refreshList();
    }
  },

  onPullDownRefresh() {
    this.refreshList().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新角色信息
  updateRoleInfo() {
    const currentRole = app.globalData.currentRole || 'user';
    const canManage = ['admin', 'super_admin'].includes(currentRole);

    this.setData({
      currentRole,
      canManage
    });

    this.updateTabsByRole(currentRole);
  },

  // 根据角色更新标签页
  updateTabsByRole(role) {
    let tabs = [{ key: 'all', name: '全部', count: 0 }];

    if (role === 'engineer') {
      tabs = [
        { key: 'all', name: '全部', count: 0 },
        { key: 'assigned', name: '待接单', count: 0 },
        { key: 'in_progress', name: '进行中', count: 0 },
        { key: 'completed', name: '已完成', count: 0 }
      ];
    } else if (['admin', 'super_admin'].includes(role)) {
      tabs = [
        { key: 'all', name: '全部', count: 0 },
        { key: 'pending', name: '待分配', count: 0 },
        { key: 'in_progress', name: '进行中', count: 0 },
        { key: 'completed', name: '已完成', count: 0 }
      ];
    } else {
      tabs = [
        { key: 'all', name: '全部', count: 0 },
        { key: 'pending', name: '待处理', count: 0 },
        { key: 'in_progress', name: '进行中', count: 0 },
        { key: 'completed', name: '已完成', count: 0 }
      ];
    }

    this.setData({ tabs });
  },

  // 角色切换回调
  onRoleChange(e) {
    const { newRole } = e.detail;
    this.updateRoleInfo();
    this.setData({ activeTab: 'all' });
    this.refreshList();
  },

  // 切换标签页
  onTabChange(e) {
    const { tab } = e.currentTarget.dataset;
    if (tab === this.data.activeTab) return;

    this.setData({
      activeTab: tab,
      currentPage: 1,
      hasMore: true
    });
    this.loadOrderList();
  },

  // 加载工单列表
  async loadOrderList(isLoadMore = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const page = isLoadMore ? this.data.currentPage + 1 : 1;
      const endpoint = this.getEndpointByRole();

      const result = await app.request({
        url: endpoint,
        method: 'GET',
        data: {
          page,
          limit: this.data.pageSize,
          status: this.data.activeTab === 'all' ? '' : this.data.activeTab
        }
      });

      if (result.success) {
        const { list, pagination, stats } = result.data;

        this.setData({
          orderList: isLoadMore ? [...this.data.orderList, ...list] : list,
          currentPage: page,
          hasMore: page < pagination.totalPages,
          stats: stats || this.data.stats
        });

        this.updateTabCounts(stats);
      }
    } catch (error) {
      console.error('加载工单列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 根据角色获取接口端点
  getEndpointByRole() {
    const role = this.data.currentRole;

    if (role === 'engineer') {
      return '/orders/assigned';
    } else if (['admin', 'super_admin'].includes(role)) {
      return '/orders';
    } else {
      return '/orders/my';
    }
  },

  // 更新标签页计数
  updateTabCounts(stats) {
    if (!stats) return;

    // 这里可以根据需要更新标签页的计数显示
    // 目前暂时不做处理，避免报错
    console.log('Tab counts updated:', stats);
  },

  // 设置过滤条件（从首页跳转时使用）
  setFilter(filter) {
    console.log('Setting filter:', filter);

    // 根据过滤条件设置当前状态
    this.setData({
      activeTab: filter === 'all' ? 'all' : filter
    });

    // 重新加载数据
    this.loadOrderList();
  },

  // 刷新列表
  refreshList() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      orderList: []
    });
    this.loadOrderList();
  },

  // 加载更多
  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadOrderList(true);
    }
  },

  // 工单点击
  onOrderTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/orders/detail/detail?id=${id}`
    });
  },

  // 创建工单
  onCreateOrder() {
    wx.navigateTo({
      url: '/pages/orders/create/create'
    });
  },

  // 接受工单
  async onAcceptOrder(e) {
    const { id } = e.currentTarget.dataset;

    try {
      const result = await app.request({
        url: `/orders/${id}/accept`,
        method: 'POST'
      });

      if (result.success) {
        wx.showToast({
          title: '接受成功',
          icon: 'success'
        });
        this.refreshList();
      }
    } catch (error) {
      console.error('接受工单失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  // 完成工单
  async onCompleteOrder(e) {
    const { id } = e.currentTarget.dataset;

    wx.showModal({
      title: '确认完成',
      content: '确定要标记此工单为已完成吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await app.request({
              url: `/orders/${id}/complete`,
              method: 'POST'
            });

            if (result.success) {
              wx.showToast({
                title: '操作成功',
                icon: 'success'
              });
              this.refreshList();
            }
          } catch (error) {
            console.error('完成工单失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 获取空数据提示文本
  getEmptyText() {
    const role = this.data.currentRole;
    const tab = this.data.activeTab;

    if (role === 'engineer') {
      if (tab === 'assigned') return '暂无待接单工单';
      if (tab === 'in_progress') return '暂无进行中工单';
      if (tab === 'completed') return '暂无已完成工单';
      return '暂无工单数据';
    } else if (['admin', 'super_admin'].includes(role)) {
      if (tab === 'pending') return '暂无待分配工单';
      if (tab === 'in_progress') return '暂无进行中工单';
      if (tab === 'completed') return '暂无已完成工单';
      return '暂无工单数据';
    } else {
      if (tab === 'pending') return '暂无待处理工单';
      if (tab === 'in_progress') return '暂无进行中工单';
      if (tab === 'completed') return '暂无已完成工单';
      return '您还没有创建任何工单';
    }
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待处理',
      'assigned': '已分配',
      'accepted': '已接受',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知';
  },

  // 获取状态样式
  getStatusClass(status) {
    const classMap = {
      'pending': 'status-pending',
      'assigned': 'status-assigned',
      'accepted': 'status-accepted',
      'in_progress': 'status-processing',
      'completed': 'status-completed',
      'cancelled': 'status-cancelled'
    };
    return classMap[status] || '';
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      'low': '低',
      'medium': '中',
      'high': '高',
      'urgent': '紧急'
    };
    return priorityMap[priority] || '中';
  },

  // 获取优先级样式
  getPriorityClass(priority) {
    const classMap = {
      'low': 'priority-low',
      'medium': 'priority-medium',
      'high': 'priority-high',
      'urgent': 'priority-urgent'
    };
    return classMap[priority] || 'priority-medium';
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
    if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
    if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';

    return timeStr.split(' ')[0]; // 返回日期部分
  },

  // 是否显示快捷操作
  showQuickActions(order) {
    const role = this.data.currentRole;
    return role === 'engineer' && (order.status === 'assigned' || order.status === 'in_progress');
  },

  // 是否可以接受
  canAccept(order) {
    const role = this.data.currentRole;
    return role === 'engineer' && order.status === 'assigned';
  },

  // 是否可以完成
  canComplete(order) {
    const role = this.data.currentRole;
    return role === 'engineer' && order.status === 'in_progress';
  }
});