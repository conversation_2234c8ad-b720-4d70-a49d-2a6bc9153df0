/* pages/orders/create/create.wxss */
.create-order-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  padding: 40rpx 30rpx 30rpx;
  color: #ffffff;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 表单样式 */
.order-form {
  padding: 20rpx 30rpx;
}

.form-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #666666;
  font-weight: normal;
}

.form-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item.required .label::after {
  content: '*';
  color: #ff4444;
  margin-left: 4rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
  background: #ffffff;
}

.input:focus {
  border-color: #1976D2;
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
  background: #ffffff;
  box-sizing: border-box;
}

.textarea:focus {
  border-color: #1976D2;
}

.picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
  background: #ffffff;
}

.picker-arrow {
  font-size: 32rpx;
  color: #cccccc;
}

/* 设备选择器 */
.equipment-selector {
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #ffffff;
}

.selected-equipment {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
}

.equipment-info {
  flex: 1;
}

.equipment-name {
  display: block;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.equipment-location {
  font-size: 24rpx;
  color: #666666;
}

.change-text {
  font-size: 26rpx;
  color: #1976D2;
}

.select-placeholder {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #999999;
}

.arrow {
  font-size: 32rpx;
  color: #cccccc;
}

/* 图片上传 */
.image-upload {
  padding: 20rpx 30rpx 30rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4444;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #cccccc;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.upload-icon {
  font-size: 48rpx;
  color: #cccccc;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999999;
}

/* 操作按钮 */
.form-actions {
  display: flex;
  gap: 20rpx;
  padding: 40rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #1976D2;
  color: #ffffff;
}

.btn-primary:active {
  background: #1565C0;
}

.btn-outline {
  background: #ffffff;
  color: #1976D2;
  border: 2rpx solid #1976D2;
}

.btn-outline:active {
  background: #f5f5f5;
}

.btn-disabled {
  background: #cccccc !important;
  color: #ffffff !important;
  border-color: #cccccc !important;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .form-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .image-list {
    justify-content: space-between;
  }

  .image-item,
  .upload-btn {
    width: calc(33.333% - 14rpx);
  }
}