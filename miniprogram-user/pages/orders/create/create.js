// pages/orders/create/create.js
const app = getApp();

Page({
  data: {
    formData: {
      title: '',
      description: '',
      priority: 'medium',
      equipmentId: '',
      faultType: 'hardware',
      urgentLevel: 'normal',
      contactPhone: '',
      expectedDate: '',
      images: []
    },
    priorities: [
      { value: 'low', label: '低' },
      { value: 'medium', label: '中' },
      { value: 'high', label: '高' },
      { value: 'urgent', label: '紧急' }
    ],
    faultTypes: [
      { value: 'hardware', label: '硬件故障' },
      { value: 'software', label: '软件问题' },
      { value: 'maintenance', label: '定期保养' },
      { value: 'calibration', label: '校准检测' },
      { value: 'other', label: '其他问题' }
    ],
    urgentLevels: [
      { value: 'normal', label: '正常' },
      { value: 'urgent', label: '加急' },
      { value: 'emergency', label: '紧急' }
    ],
    equipment: null,
    loading: false,
    currentRole: 'user'
  },

  onLoad(options) {
    console.log('创建工单页面参数:', options);

    // 处理普通跳转传递的设备ID
    const { equipmentId } = options;
    if (equipmentId) {
      this.setData({
        'formData.equipmentId': equipmentId
      });
      this.loadEquipmentInfo(equipmentId);
    }

    // 处理二维码扫描传递的参数
    this.handleQRCodeParams(options);

    this.checkAuth();
    this.updateRoleInfo();
    this.initUserInfo();
  },

  // 处理二维码扫描参数
  handleQRCodeParams(options) {
    // 检查是否有二维码扫描的参数 q
    if (options.q) {
      try {
        // 解码二维码内容
        const qrUrl = decodeURIComponent(options.q);
        console.log('二维码扫描URL:', qrUrl);

        // 解析URL参数
        const urlParams = this.parseUrlParams(qrUrl);
        console.log('解析的URL参数:', urlParams);

        if (urlParams.id || urlParams.sn) {
          // 优先使用序列号，其次使用ID
          const identifier = urlParams.sn || urlParams.id;
          const identifierType = urlParams.sn ? 'serialNumber' : 'id';

          // 通过序列号或ID获取完整设备信息
          this.loadEquipmentByIdentifier(identifier, identifierType);
        }

        // 记录扫码时间
        if (options.scancode_time) {
          console.log('扫码时间:', new Date(parseInt(options.scancode_time) * 1000));
        }

      } catch (error) {
        console.error('解析二维码参数失败:', error);
        wx.showToast({
          title: '二维码格式错误',
          icon: 'none'
        });
      }
    }

    // 处理直接传递的设备信息参数（兼容性处理）
    if (options.id && options.source === 'qrcode') {
      this.loadEquipmentByIdentifier(options.id, 'id');
    }

    // 处理序列号参数
    if (options.sn && options.source === 'qrcode') {
      this.loadEquipmentByIdentifier(options.sn, 'serialNumber');
    }
  },

  // 通过标识符加载设备信息
  async loadEquipmentByIdentifier(identifier, type = 'id') {
    wx.showLoading({
      title: '加载设备信息...',
      mask: true
    });

    try {
      console.log(`通过${type}加载设备信息:`, identifier);

      // 调用API获取设备详细信息
      const result = await app.request({
        url: '/equipment/getByIdentifier',
        method: 'GET',
        data: {
          identifier: identifier,
          type: type // 'id' 或 'serialNumber'
        }
      });

      if (result.success && result.data) {
        const equipment = result.data;

        // 设置设备信息
        this.setData({
          equipment: equipment,
          'formData.equipmentId': equipment.id
        });

        // 自动生成工单标题
        const autoTitle = `${equipment.name}${equipment.model ? (' - ' + equipment.model) : ''} 维修申请`;
        this.setData({
          'formData.title': autoTitle
        });

        wx.showToast({
          title: '已自动填入设备信息',
          icon: 'success',
          duration: 2000
        });

        console.log('设备信息加载成功:', equipment);
      } else {
        throw new Error(result.message || '设备信息不存在');
      }
    } catch (error) {
      console.error('加载设备信息失败:', error);

      // 如果API调用失败，尝试使用本地模拟数据
      this.loadMockEquipmentData(identifier, type);
    } finally {
      wx.hideLoading();
    }
  },

  // 加载模拟设备数据（用于开发测试）
  loadMockEquipmentData(identifier, type) {
    console.log('使用模拟数据:', identifier, type);

    // 模拟设备数据
    const mockEquipment = {
      id: identifier,
      name: '激光打印机',
      model: 'HP LaserJet Pro M404n',
      serialNumber: identifier,
      location: '办公室A-101',
      status: 'normal',
      description: '通过二维码扫描获取的设备信息'
    };

    this.setData({
      equipment: mockEquipment,
      'formData.equipmentId': mockEquipment.id
    });

    const autoTitle = `${mockEquipment.name} - ${mockEquipment.model} 维修申请`;
    this.setData({
      'formData.title': autoTitle
    });

    wx.showToast({
      title: '已填入模拟设备信息',
      icon: 'success',
      duration: 2000
    });
  },

  // 解析URL参数
  parseUrlParams(url) {
    const params = {};
    try {
      // 提取查询字符串部分
      const queryString = url.split('?')[1] || '';
      const pairs = queryString.split('&');

      pairs.forEach(pair => {
        const [key, value] = pair.split('=');
        if (key && value) {
          params[decodeURIComponent(key)] = decodeURIComponent(value);
        }
      });
    } catch (error) {
      console.error('解析URL参数失败:', error);
    }

    return params;
  },

  onShow() {
    this.updateRoleInfo();
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新角色信息
  updateRoleInfo() {
    const currentRole = app.globalData.currentRole || 'user';
    this.setData({ currentRole });
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = app.globalData.userInfo;
    if (userInfo && userInfo.phone) {
      this.setData({
        'formData.contactPhone': userInfo.phone
      });
    }
  },

  // 加载设备信息
  async loadEquipmentInfo(equipmentId) {
    try {
      const result = await app.request({
        url: `/equipment/${equipmentId}`,
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          equipment: result.data
        });
      }
    } catch (error) {
      console.error('加载设备信息失败:', error);
    }
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择器变化
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const options = this.data[field + 's'];

    this.setData({
      [`formData.${field}`]: options[value].value
    });
  },

  // 日期选择
  onDateChange(e) {
    const { value } = e.detail;
    this.setData({
      'formData.expectedDate': value
    });
  },

  // 选择设备
  onSelectEquipment() {
    wx.navigateTo({
      url: '/pages/equipment/select/select'
    });
  },

  // 上传图片
  onUploadImage() {
    const maxImages = 6;
    const currentCount = this.data.formData.images.length;

    if (currentCount >= maxImages) {
      wx.showToast({
        title: `最多上传${maxImages}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: maxImages - currentCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        this.uploadImages(tempFilePaths);
      }
    });
  },

  // 上传图片到服务器
  async uploadImages(filePaths) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    try {
      const uploadPromises = filePaths.map(filePath => {
        return new Promise((resolve, reject) => {
          // 模拟上传
          setTimeout(() => {
            resolve(`/images/upload/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`);
          }, 1000);
        });
      });

      const uploadedUrls = await Promise.all(uploadPromises);

      this.setData({
        'formData.images': [...this.data.formData.images, ...uploadedUrls]
      });

      wx.showToast({
        title: '上传成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('上传图片失败:', error);
      wx.showToast({
        title: '上传失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 删除图片
  onDeleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const images = [...this.data.formData.images];
    images.splice(index, 1);

    this.setData({
      'formData.images': images
    });
  },

  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: this.data.formData.images
    });
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;

    if (!formData.title.trim()) {
      wx.showToast({
        title: '请填写工单标题',
        icon: 'none'
      });
      return false;
    }

    if (!formData.description.trim()) {
      wx.showToast({
        title: '请描述故障详情',
        icon: 'none'
      });
      return false;
    }

    if (!formData.equipmentId) {
      wx.showToast({
        title: '请选择设备',
        icon: 'none'
      });
      return false;
    }

    if (!formData.contactPhone.trim()) {
      wx.showToast({
        title: '请填写联系电话',
        icon: 'none'
      });
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.contactPhone)) {
      wx.showToast({
        title: '请填写正确的手机号',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 提交工单
  async onSubmit() {
    if (!this.validateForm()) return;
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const result = await app.request({
        url: '/orders',
        method: 'POST',
        data: this.data.formData
      });

      if (result.success) {
        wx.showToast({
          title: '工单创建成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('创建工单失败:', error);
      wx.showToast({
        title: '创建失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 重置表单
  onReset() {
    wx.showModal({
      title: '确认重置',
      content: '确定要清空所有输入内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            formData: {
              title: '',
              description: '',
              priority: 'medium',
              equipmentId: this.data.formData.equipmentId, // 保留设备ID
              faultType: 'hardware',
              urgentLevel: 'normal',
              contactPhone: app.globalData.userInfo?.phone || '',
              expectedDate: '',
              images: []
            }
          });
        }
      }
    });
  }
});