<!--pages/orders/create/create.wxml-->
<view class="create-order-container">
  <view class="page-header">
    <text class="page-title">创建工单</text>
    <text class="page-subtitle">请详细描述设备故障情况</text>
  </view>

  <form class="order-form">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <view class="form-item required">
        <text class="label">工单标题</text>
        <input
          class="input"
          placeholder="请简要描述故障问题"
          value="{{formData.title}}"
          data-field="title"
          bindinput="onInputChange"
          maxlength="50"
        />
      </view>

      <view class="form-item required">
        <text class="label">故障描述</text>
        <textarea
          class="textarea"
          placeholder="请详细描述故障现象、发生时间、影响范围等"
          value="{{formData.description}}"
          data-field="description"
          bindinput="onInputChange"
          maxlength="500"
        ></textarea>
      </view>

      <view class="form-item required">
        <text class="label">故障设备</text>
        <view class="equipment-selector" bindtap="onSelectEquipment">
          <view wx:if="{{equipment}}" class="selected-equipment">
            <view class="equipment-info">
              <text class="equipment-name">{{equipment.name}}</text>
              <text class="equipment-location">{{equipment.location}}</text>
            </view>
            <text class="change-text">更换</text>
          </view>
          <view wx:else class="select-placeholder">
            <text>请选择设备</text>
            <text class="arrow">›</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="label">故障类型</text>
        <picker
          mode="selector"
          range="{{faultTypes}}"
          range-key="label"
          data-field="faultType"
          bindchange="onPickerChange"
        >
          <view class="picker">
            {{faultTypes[0].label}}
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 优先级设置 -->
    <view class="form-section">
      <view class="section-title">优先级设置</view>

      <view class="form-item">
        <text class="label">优先级</text>
        <picker
          mode="selector"
          range="{{priorities}}"
          range-key="label"
          data-field="priority"
          bindchange="onPickerChange"
        >
          <view class="picker">
            {{priorities[1].label}}
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">紧急程度</text>
        <picker
          mode="selector"
          range="{{urgentLevels}}"
          range-key="label"
          data-field="urgentLevel"
          bindchange="onPickerChange"
        >
          <view class="picker">
            {{urgentLevels[0].label}}
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">期望完成时间</text>
        <picker
          mode="date"
          value="{{formData.expectedDate}}"
          bindchange="onDateChange"
        >
          <view class="picker">
            {{formData.expectedDate || '请选择期望完成时间'}}
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 联系信息 -->
    <view class="form-section">
      <view class="section-title">联系信息</view>

      <view class="form-item required">
        <text class="label">联系电话</text>
        <input
          class="input"
          type="number"
          placeholder="请输入联系电话"
          value="{{formData.contactPhone}}"
          data-field="contactPhone"
          bindinput="onInputChange"
          maxlength="11"
        />
      </view>
    </view>

    <!-- 图片上传 -->
    <view class="form-section">
      <view class="section-title">
        <text>故障图片</text>
        <text class="section-subtitle">（可选，最多6张）</text>
      </view>

      <view class="image-upload">
        <view class="image-list">
          <view class="image-item"
                wx:for="{{formData.images}}"
                wx:key="index">
            <image
              class="uploaded-image"
              src="{{item}}"
              mode="aspectFill"
              data-url="{{item}}"
              bindtap="onPreviewImage"
            ></image>
            <view class="delete-btn"
                  data-index="{{index}}"
                  bindtap="onDeleteImage">×</view>
          </view>

          <view wx:if="{{formData.images.length < 6}}"
                class="upload-btn"
                bindtap="onUploadImage">
            <text class="upload-icon">+</text>
            <text class="upload-text">添加图片</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button
        class="btn btn-outline"
        bindtap="onReset"
        disabled="{{loading}}"
      >
        重置
      </button>

      <button
        class="btn btn-primary {{loading ? 'btn-disabled' : ''}}"
        bindtap="onSubmit"
        disabled="{{loading}}"
      >
        {{loading ? '提交中...' : '提交工单'}}
      </button>
    </view>
  </form>
</view>