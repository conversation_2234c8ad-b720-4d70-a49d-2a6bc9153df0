/* pages/orders/orders.wxss */
.orders-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  padding: 40rpx 30rpx 20rpx;
  color: #ffffff;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
}

.header-role-switcher {
  opacity: 0.9;
}

/* 标签页 */
.tabs-container {
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs {
  display: flex;
  padding: 0 30rpx;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  margin-right: 40rpx;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1976D2;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #1976D2;
  border-radius: 2rpx;
}

.tab-name {
  font-size: 28rpx;
  font-weight: 500;
}

.tab-count {
  margin-left: 8rpx;
  padding: 2rpx 8rpx;
  background: #ff4444;
  color: #ffffff;
  font-size: 20rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
}

/* 内容区域 */
.orders-content {
  padding: 20rpx 30rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空数据状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-action-btn {
  background: #1976D2;
  color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

/* 工单列表 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.order-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 工单头部 */
.order-header {
  margin-bottom: 20rpx;
}

.order-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.order-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  margin-right: 20rpx;
}

.order-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  white-space: nowrap;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-assigned {
  background: #cce5ff;
  color: #0066cc;
}

.status-accepted {
  background: #d4edda;
  color: #155724;
}

.status-processing {
  background: #e2e3f1;
  color: #6c757d;
}

.status-completed {
  background: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background: #f8d7da;
  color: #721c24;
}

.order-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number {
  font-size: 24rpx;
  color: #666666;
}

.order-priority {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.priority-low {
  background: #f8f9fa;
  color: #6c757d;
}

.priority-medium {
  background: #fff3cd;
  color: #856404;
}

.priority-high {
  background: #ffe6cc;
  color: #cc6600;
}

.priority-urgent {
  background: #f8d7da;
  color: #721c24;
}

/* 工单内容 */
.order-content {
  margin-bottom: 20rpx;
}

.equipment-info {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  padding: 12rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.equipment-label {
  font-size: 24rpx;
  color: #666666;
  margin-right: 8rpx;
}

.equipment-name {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.order-info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 24rpx;
  color: #666666;
  margin-right: 8rpx;
}

.info-value {
  font-size: 24rpx;
  color: #333333;
}

/* 工单操作 */
.order-actions {
  display: flex;
  gap: 16rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
}

.action-btn.primary {
  background: #1976D2;
  color: #ffffff;
}

.action-btn.success {
  background: #4CAF50;
  color: #ffffff;
}

/* 加载更多 */
.load-more {
  padding: 40rpx 0;
  text-align: center;
}

.load-more-btn {
  background: #ffffff;
  color: #1976D2;
  border: 2rpx solid #1976D2;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.no-more {
  padding: 40rpx 0;
  text-align: center;
  color: #999999;
  font-size: 26rpx;
}

/* 悬浮按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 30rpx;
  width: 112rpx;
  height: 112rpx;
  background: #1976D2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
}

.fab:active {
  transform: scale(0.9);
}

.fab-icon {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: 300;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .order-title-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .order-title {
    margin-right: 0;
    margin-bottom: 12rpx;
  }

  .order-info-row {
    flex-direction: column;
    gap: 8rpx;
  }

  .order-actions {
    flex-direction: column;
  }
}