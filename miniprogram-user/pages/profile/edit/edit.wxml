<!--pages/profile/edit/edit.wxml-->
<view class="edit-profile-container">
  <view class="page-header">
    <text class="page-title">编辑个人信息</text>
  </view>

  <form class="profile-form">
    <!-- 头像设置 -->
    <view class="form-section">
      <view class="section-title">头像设置</view>
      <view class="avatar-section">
        <view class="avatar-container" bindtap="onChooseAvatar">
          <image
            class="avatar-image"
            src="{{formData.avatar || '/images/default-avatar.png'}}"
            mode="aspectFill"
          ></image>
          <view class="avatar-overlay">
            <text class="avatar-text">{{avatarUploading ? '上传中...' : '点击更换'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <view class="form-item required">
        <text class="label">用户名</text>
        <input
          class="input"
          placeholder="请输入用户名"
          value="{{formData.username}}"
          data-field="username"
          bindinput="onInputChange"
          maxlength="20"
        />
      </view>

      <view class="form-item">
        <text class="label">手机号</text>
        <input
          class="input"
          type="number"
          placeholder="请输入手机号"
          value="{{formData.phone}}"
          data-field="phone"
          bindinput="onInputChange"
          maxlength="11"
        />
      </view>

      <view class="form-item">
        <text class="label">邮箱</text>
        <input
          class="input"
          placeholder="请输入邮箱地址"
          value="{{formData.email}}"
          data-field="email"
          bindinput="onInputChange"
        />
      </view>
    </view>

    <!-- 工作信息 -->
    <view class="form-section">
      <view class="section-title">工作信息</view>

      <view class="form-item">
        <text class="label">所属部门</text>
        <input
          class="input"
          placeholder="请输入所属部门"
          value="{{formData.department}}"
          data-field="department"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="label">课题组</text>
        <input
          class="input"
          placeholder="请输入课题组名称"
          value="{{formData.researchGroup}}"
          data-field="researchGroup"
          bindinput="onInputChange"
        />
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button
        class="btn btn-outline"
        bindtap="onReset"
        disabled="{{loading}}"
      >
        重置
      </button>

      <button
        class="btn btn-primary {{loading ? 'btn-disabled' : ''}}"
        bindtap="onSave"
        disabled="{{loading || avatarUploading}}"
      >
        {{loading ? '保存中...' : '保存'}}
      </button>
    </view>
  </form>
</view>