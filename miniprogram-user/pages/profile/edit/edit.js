// pages/profile/edit/edit.js
const app = getApp();

Page({
  data: {
    formData: {
      username: '',
      phone: '',
      email: '',
      department: '',
      researchGroup: '',
      avatar: ''
    },
    loading: false,
    avatarUploading: false
  },

  onLoad() {
    this.checkAuth();
    this.initUserInfo();
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = app.globalData.userInfo;
    if (userInfo) {
      this.setData({
        formData: {
          username: userInfo.username || '',
          phone: userInfo.phone || '',
          email: userInfo.email || '',
          department: userInfo.department || '',
          researchGroup: userInfo.researchGroup || '',
          avatar: userInfo.avatar || ''
        }
      });
    }
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择头像
  onChooseAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadAvatar(tempFilePath);
      }
    });
  },

  // 上传头像
  async uploadAvatar(filePath) {
    this.setData({ avatarUploading: true });

    try {
      // 模拟上传
      await new Promise(resolve => setTimeout(resolve, 1500));
      const avatarUrl = `/images/avatar/${Date.now()}.jpg`;

      this.setData({
        'formData.avatar': avatarUrl
      });

      wx.showToast({
        title: '头像上传成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('上传头像失败:', error);
      wx.showToast({
        title: '上传失败',
        icon: 'error'
      });
    } finally {
      this.setData({ avatarUploading: false });
    }
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;

    if (!formData.username.trim()) {
      wx.showToast({
        title: '请填写用户名',
        icon: 'none'
      });
      return false;
    }

    if (formData.phone && !/^1[3-9]\d{9}$/.test(formData.phone)) {
      wx.showToast({
        title: '请填写正确的手机号',
        icon: 'none'
      });
      return false;
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      wx.showToast({
        title: '请填写正确的邮箱',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 保存修改
  async onSave() {
    if (!this.validateForm()) return;
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const result = await app.request({
        url: '/users/profile',
        method: 'PUT',
        data: this.data.formData
      });

      if (result.success) {
        // 更新全局用户信息
        const updatedUserInfo = { ...app.globalData.userInfo, ...this.data.formData };
        app.globalData.userInfo = updatedUserInfo;
        wx.setStorageSync('userInfo', updatedUserInfo);

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('保存失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 重置表单
  onReset() {
    wx.showModal({
      title: '确认重置',
      content: '确定要恢复到原始信息吗？',
      success: (res) => {
        if (res.confirm) {
          this.initUserInfo();
        }
      }
    });
  }
});