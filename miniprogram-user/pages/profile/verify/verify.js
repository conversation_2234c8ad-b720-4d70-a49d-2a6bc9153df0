// pages/profile/verify/verify.js
const app = getApp();

Page({
  data: {
    formData: {
      realName: '',
      idCard: '',
      frontImage: '',
      backImage: ''
    },
    loading: false,
    uploadingFront: false,
    uploadingBack: false,
    isVerified: false,
    verifyStatus: 'pending' // pending, processing, approved, rejected
  },

  onLoad() {
    this.checkAuth();
    this.checkVerifyStatus();
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 检查认证状态
  async checkVerifyStatus() {
    try {
      const result = await app.request({
        url: '/users/verify-status',
        method: 'GET'
      });

      if (result.success) {
        const { isVerified, status, realName, idCard } = result.data;
        this.setData({
          isVerified,
          verifyStatus: status,
          'formData.realName': realName || '',
          'formData.idCard': idCard || ''
        });
      }
    } catch (error) {
      console.error('获取认证状态失败:', error);
    }
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 上传身份证正面
  onUploadFront() {
    if (this.data.isVerified) {
      wx.showToast({
        title: '已通过认证，无需重复上传',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadIdCardImage(tempFilePath, 'front');
      }
    });
  },

  // 上传身份证背面
  onUploadBack() {
    if (this.data.isVerified) {
      wx.showToast({
        title: '已通过认证，无需重复上传',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadIdCardImage(tempFilePath, 'back');
      }
    });
  },

  // 上传身份证图片
  async uploadIdCardImage(filePath, type) {
    const loadingKey = type === 'front' ? 'uploadingFront' : 'uploadingBack';
    this.setData({ [loadingKey]: true });

    try {
      // 模拟上传
      await new Promise(resolve => setTimeout(resolve, 2000));
      const imageUrl = `/images/idcard/${type}_${Date.now()}.jpg`;

      this.setData({
        [`formData.${type}Image`]: imageUrl
      });

      wx.showToast({
        title: '上传成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('上传失败:', error);
      wx.showToast({
        title: '上传失败',
        icon: 'error'
      });
    } finally {
      this.setData({ [loadingKey]: false });
    }
  },

  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: [url]
    });
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;

    if (!formData.realName.trim()) {
      wx.showToast({
        title: '请填写真实姓名',
        icon: 'none'
      });
      return false;
    }

    if (!formData.idCard.trim()) {
      wx.showToast({
        title: '请填写身份证号',
        icon: 'none'
      });
      return false;
    }

    // 简单的身份证号验证
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardRegex.test(formData.idCard)) {
      wx.showToast({
        title: '请填写正确的身份证号',
        icon: 'none'
      });
      return false;
    }

    if (!formData.frontImage) {
      wx.showToast({
        title: '请上传身份证正面照片',
        icon: 'none'
      });
      return false;
    }

    if (!formData.backImage) {
      wx.showToast({
        title: '请上传身份证背面照片',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 提交认证
  async onSubmit() {
    if (this.data.isVerified) {
      wx.showToast({
        title: '您已通过实名认证',
        icon: 'none'
      });
      return;
    }

    if (!this.validateForm()) return;
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const result = await app.request({
        url: '/users/verify',
        method: 'POST',
        data: this.data.formData
      });

      if (result.success) {
        wx.showToast({
          title: '提交成功，请等待审核',
          icon: 'success'
        });

        this.setData({
          verifyStatus: 'processing'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('提交认证失败:', error);
      wx.showToast({
        title: '提交失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '未认证',
      'processing': '审核中',
      'approved': '已通过',
      'rejected': '已拒绝'
    };
    return statusMap[status] || '未知';
  },

  // 获取状态样式
  getStatusClass(status) {
    const classMap = {
      'pending': 'status-pending',
      'processing': 'status-processing',
      'approved': 'status-approved',
      'rejected': 'status-rejected'
    };
    return classMap[status] || '';
  }
});