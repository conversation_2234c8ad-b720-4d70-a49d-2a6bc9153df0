<!--pages/profile/verify/verify.wxml-->
<view class="verify-container">
  <view class="page-header">
    <text class="page-title">实名认证</text>
    <view class="status-badge {{getStatusClass(verifyStatus)}}">
      {{getStatusText(verifyStatus)}}
    </view>
  </view>

  <!-- 认证说明 -->
  <view class="info-section">
    <view class="info-title">认证说明</view>
    <view class="info-content">
      <text class="info-text">• 实名认证后可享受更多服务权限</text>
      <text class="info-text">• 请确保上传的身份证信息清晰可见</text>
      <text class="info-text">• 认证信息仅用于身份验证，严格保密</text>
      <text class="info-text">• 审核时间通常为1-3个工作日</text>
    </view>
  </view>

  <!-- 认证表单 -->
  <form class="verify-form">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <view class="form-item required">
        <text class="label">真实姓名</text>
        <input
          class="input {{isVerified ? 'input-disabled' : ''}}"
          placeholder="请输入真实姓名"
          value="{{formData.realName}}"
          data-field="realName"
          bindinput="onInputChange"
          disabled="{{isVerified}}"
          maxlength="10"
        />
      </view>

      <view class="form-item required">
        <text class="label">身份证号</text>
        <input
          class="input {{isVerified ? 'input-disabled' : ''}}"
          placeholder="请输入身份证号码"
          value="{{formData.idCard}}"
          data-field="idCard"
          bindinput="onInputChange"
          disabled="{{isVerified}}"
          maxlength="18"
        />
      </view>
    </view>

    <!-- 身份证照片 -->
    <view class="form-section">
      <view class="section-title">身份证照片</view>

      <!-- 正面照片 -->
      <view class="upload-item">
        <text class="upload-label">身份证正面</text>
        <view class="upload-area" bindtap="onUploadFront">
          <view wx:if="{{formData.frontImage}}" class="uploaded-image">
            <image
              class="id-image"
              src="{{formData.frontImage}}"
              mode="aspectFit"
              data-url="{{formData.frontImage}}"
              bindtap="onPreviewImage"
            ></image>
            <view wx:if="{{!isVerified}}" class="image-overlay">
              <text class="overlay-text">{{uploadingFront ? '上传中...' : '点击重新上传'}}</text>
            </view>
          </view>
          <view wx:else class="upload-placeholder">
            <view class="upload-icon">📷</view>
            <text class="upload-text">{{uploadingFront ? '上传中...' : '点击上传身份证正面'}}</text>
            <text class="upload-hint">请确保证件信息清晰可见</text>
          </view>
        </view>
      </view>

      <!-- 背面照片 -->
      <view class="upload-item">
        <text class="upload-label">身份证背面</text>
        <view class="upload-area" bindtap="onUploadBack">
          <view wx:if="{{formData.backImage}}" class="uploaded-image">
            <image
              class="id-image"
              src="{{formData.backImage}}"
              mode="aspectFit"
              data-url="{{formData.backImage}}"
              bindtap="onPreviewImage"
            ></image>
            <view wx:if="{{!isVerified}}" class="image-overlay">
              <text class="overlay-text">{{uploadingBack ? '上传中...' : '点击重新上传'}}</text>
            </view>
          </view>
          <view wx:else class="upload-placeholder">
            <view class="upload-icon">📷</view>
            <text class="upload-text">{{uploadingBack ? '上传中...' : '点击上传身份证背面'}}</text>
            <text class="upload-hint">请确保证件信息清晰可见</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view wx:if="{{!isVerified && verifyStatus !== 'processing'}}" class="form-actions">
      <button
        class="btn btn-primary {{loading ? 'btn-disabled' : ''}}"
        bindtap="onSubmit"
        disabled="{{loading || uploadingFront || uploadingBack}}"
      >
        {{loading ? '提交中...' : '提交认证'}}
      </button>
    </view>

    <!-- 状态提示 -->
    <view wx:if="{{verifyStatus === 'processing'}}" class="status-tip processing">
      <view class="tip-icon">⏳</view>
      <text class="tip-text">您的认证申请正在审核中，请耐心等待</text>
    </view>

    <view wx:if="{{isVerified}}" class="status-tip approved">
      <view class="tip-icon">✅</view>
      <text class="tip-text">恭喜！您已通过实名认证</text>
    </view>

    <view wx:if="{{verifyStatus === 'rejected'}}" class="status-tip rejected">
      <view class="tip-icon">❌</view>
      <text class="tip-text">认证未通过，请检查信息后重新提交</text>
    </view>
  </form>
</view>