/* pages/profile/verify/verify.wxss */
.verify-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  padding: 40rpx 30rpx 30rpx;
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending {
  background: rgba(158, 158, 158, 0.2);
  color: #9E9E9E;
}

.status-processing {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
}

.status-approved {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.status-rejected {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}

/* 信息说明 */
.info-section {
  background: #ffffff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 表单样式 */
.verify-form {
  padding: 0 30rpx;
}

.form-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单项 */
.form-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item.required .label::after {
  content: '*';
  color: #ff4444;
  margin-left: 4rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
  background: #ffffff;
}

.input:focus {
  border-color: #1976D2;
}

.input-disabled {
  background: #f5f5f5;
  color: #999999;
}

/* 上传区域 */
.upload-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.upload-item:last-child {
  border-bottom: none;
}

.upload-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.upload-area {
  position: relative;
  width: 100%;
  height: 300rpx;
  border: 2rpx dashed #cccccc;
  border-radius: 12rpx;
  overflow: hidden;
}

.uploaded-image {
  position: relative;
  width: 100%;
  height: 100%;
}

.id-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.uploaded-image:active .image-overlay {
  opacity: 1;
}

.overlay-text {
  color: #ffffff;
  font-size: 26rpx;
  text-align: center;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fafafa;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.upload-hint {
  font-size: 24rpx;
  color: #999999;
}

/* 操作按钮 */
.form-actions {
  padding: 40rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.btn {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}

.btn-primary {
  background: #1976D2;
  color: #ffffff;
}

.btn-disabled {
  background: #cccccc !important;
  color: #ffffff !important;
}

/* 状态提示 */
.status-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.tip-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #333333;
  text-align: center;
}

.status-tip.processing {
  background: #fff8e1;
}

.status-tip.approved {
  background: #e8f5e8;
}

.status-tip.rejected {
  background: #ffebee;
}