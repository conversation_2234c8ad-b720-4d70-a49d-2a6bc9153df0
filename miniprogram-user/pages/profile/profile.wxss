/* pages/profile/profile.wxss */
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 用户信息卡片 */
.user-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  padding: 40rpx 30rpx;
  color: #ffffff;
}

.user-avatar {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}

.user-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.verified-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32rpx;
  height: 32rpx;
  background: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #ffffff;
  border: 2rpx solid #ffffff;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-role {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.user-department {
  font-size: 24rpx;
  opacity: 0.8;
}

.user-actions {
  margin-left: 20rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  font-size: 26rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

/* 角色切换器 */
.role-section {
  margin: 20rpx 30rpx;
}

.profile-role-switcher {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 统计信息 */
.stats-section {
  margin: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.stats-grid {
  display: flex;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 20rpx;
  border-right: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-item:last-child {
  border-right: none;
}

.stat-item:active {
  background: #f8f9fa;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1976D2;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

/* 功能菜单 */
.menu-section {
  margin: 20rpx 30rpx;
}

.menu-group {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f8f9fa;
}

.menu-item.logout {
  color: #ff4444;
}

.menu-item.logout:active {
  background: #fff5f5;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.menu-item.logout .menu-text {
  color: #ff4444;
}

.menu-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 24rpx;
  margin-right: 16rpx;
}

.status-text.verified {
  color: #4CAF50;
}

.status-text.unverified {
  color: #ff9800;
}

.menu-arrow {
  font-size: 32rpx;
  color: #cccccc;
}

.arrow-icon {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 300;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 40rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999999;
}