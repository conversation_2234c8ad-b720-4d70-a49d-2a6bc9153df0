/* components/role-switcher/role-switcher.wxss */

/* 基础样式 */
.role-switcher {
  position: relative;
}

/* 紧凑模式样式 */
.role-switcher-compact {
  display: inline-block;
}

.role-switcher-compact .current-role {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.role-switcher-compact .role-name {
  font-size: 24rpx;
  color: #ffffff;
  margin-right: 8rpx;
}

.role-switcher-compact .switch-icon {
  display: flex;
  align-items: center;
}

.role-switcher-compact .icon {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

/* 完整模式样式 */
.role-switcher-full {
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}

.role-header {
  padding: 20rpx 24rpx 12rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.role-label {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.current-role-full {
  display: flex;
  align-items: center;
  padding: 24rpx;
}

.role-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
}

.role-info {
  flex: 1;
}

.role-info .role-name {
  display: block;
  font-size: 32rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.role-hint {
  font-size: 24rpx;
  color: #999999;
}

.current-role-full .switch-icon {
  margin-left: 16rpx;
}

.current-role-full .icon {
  font-size: 32rpx;
  color: #cccccc;
}

/* 角色选择弹窗 */
.role-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.role-selector {
  width: 600rpx;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.selector-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 600;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.close-icon {
  font-size: 32rpx;
  color: #666666;
}

/* 角色列表 */
.role-list {
  max-height: 60vh;
  overflow-y: auto;
}

.role-option {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.2s ease;
}

.role-option:last-child {
  border-bottom: none;
}

.role-option:active {
  background: #f5f5f5;
}

.role-option.current {
  background: #f0f8ff;
}

.option-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
}

.option-info {
  flex: 1;
}

.option-name {
  display: block;
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.option-dept {
  font-size: 24rpx;
  color: #666666;
}

.current-indicator {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1976D2;
  border-radius: 50%;
}

.check-icon {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #666666;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
