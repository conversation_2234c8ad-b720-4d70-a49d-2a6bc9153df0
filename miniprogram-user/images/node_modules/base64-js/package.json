{"name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.5.1", "author": "<PERSON><PERSON> <PERSON> Little <<EMAIL>>", "typings": "index.d.ts", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "devDependencies": {"babel-minify": "^0.5.1", "benchmark": "^2.1.4", "browserify": "^16.3.0", "standard": "*", "tape": "4.x"}, "homepage": "https://github.com/beatgammit/base64-js", "keywords": ["base64"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | minify > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}