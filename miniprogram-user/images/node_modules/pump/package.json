{"name": "pump", "version": "3.0.2", "repository": "git://github.com/mafintosh/pump.git", "license": "MIT", "description": "pipe streams together and close all of them if one of them closes", "browser": {"fs": false}, "keywords": ["streams", "pipe", "destroy", "callback"], "author": "<PERSON> <<EMAIL>>", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}, "scripts": {"test": "node test-browser.js && node test-node.js"}}