// 创建简单的 PNG 图标
// 由于无法直接转换 SVG，我们创建简单的几何图形 PNG

const fs = require('fs');

// 创建一个简单的 48x48 PNG 图标的 base64 数据
function createSimpleIcon(color, shape) {
  // 这里使用一个最小的 PNG 文件结构
  // 实际项目中建议使用专业工具转换 SVG 到 PNG
  
  const canvas = require('canvas');
  const { createCanvas } = canvas;
  
  const width = 48;
  const height = 48;
  const canvasElement = createCanvas(width, height);
  const ctx = canvasElement.getContext('2d');
  
  // 清除画布
  ctx.clearRect(0, 0, width, height);
  
  // 设置颜色
  ctx.fillStyle = color;
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  
  // 根据形状绘制图标
  switch (shape) {
    case 'home':
      // 绘制房子
      ctx.beginPath();
      ctx.moveTo(24, 8);
      ctx.lineTo(8, 20);
      ctx.lineTo(8, 40);
      ctx.lineTo(40, 40);
      ctx.lineTo(40, 20);
      ctx.closePath();
      ctx.stroke();
      
      // 门
      ctx.strokeRect(18, 28, 12, 12);
      break;
      
    case 'equipment':
      // 绘制显示器
      ctx.strokeRect(6, 10, 36, 24);
      ctx.strokeRect(20, 34, 8, 6);
      ctx.strokeRect(16, 40, 16, 2);
      
      // 指示灯
      ctx.fillRect(12, 18, 2, 2);
      ctx.fillRect(23, 18, 2, 2);
      ctx.fillRect(34, 18, 2, 2);
      break;
      
    case 'orders':
      // 绘制文档
      ctx.beginPath();
      ctx.moveTo(12, 6);
      ctx.lineTo(30, 6);
      ctx.lineTo(36, 12);
      ctx.lineTo(36, 42);
      ctx.lineTo(12, 42);
      ctx.closePath();
      ctx.stroke();
      
      // 文档折角
      ctx.beginPath();
      ctx.moveTo(30, 6);
      ctx.lineTo(30, 12);
      ctx.lineTo(36, 12);
      ctx.stroke();
      
      // 文本行
      ctx.strokeRect(16, 20, 16, 1);
      ctx.strokeRect(16, 26, 16, 1);
      ctx.strokeRect(16, 32, 12, 1);
      break;
      
    case 'chat':
      // 绘制对话框
      ctx.beginPath();
      ctx.roundRect(6, 10, 32, 20, 4);
      ctx.stroke();
      
      // 对话框尾巴
      ctx.beginPath();
      ctx.moveTo(12, 30);
      ctx.lineTo(8, 36);
      ctx.lineTo(16, 30);
      ctx.stroke();
      
      // 省略号
      ctx.fillRect(14, 18, 2, 2);
      ctx.fillRect(22, 18, 2, 2);
      ctx.fillRect(30, 18, 2, 2);
      break;
      
    case 'profile':
      // 绘制人物
      ctx.beginPath();
      ctx.arc(24, 16, 6, 0, Math.PI * 2);
      ctx.stroke();
      
      // 身体
      ctx.beginPath();
      ctx.arc(24, 36, 12, Math.PI, 0, true);
      ctx.stroke();
      break;
  }
  
  return canvasElement.toBuffer('image/png');
}

// 生成所有图标
function generateIcons() {
  const icons = [
    { name: 'home', shape: 'home' },
    { name: 'equipment', shape: 'equipment' },
    { name: 'orders', shape: 'orders' },
    { name: 'chat', shape: 'chat' },
    { name: 'profile', shape: 'profile' }
  ];
  
  icons.forEach(icon => {
    try {
      // 普通状态 (灰色)
      const normalBuffer = createSimpleIcon('#666666', icon.shape);
      fs.writeFileSync(`${icon.name}.png`, normalBuffer);
      
      // 激活状态 (蓝色)
      const activeBuffer = createSimpleIcon('#1976D2', icon.shape);
      fs.writeFileSync(`${icon.name}_active.png`, activeBuffer);
      
      console.log(`✓ 生成 ${icon.name} 图标`);
    } catch (error) {
      console.log(`✗ 生成 ${icon.name} 图标失败:`, error.message);
    }
  });
}

// 如果没有 canvas 模块，创建占位图标
function createPlaceholderIcons() {
  console.log('Canvas 模块未安装，创建占位图标...');
  
  // 创建一个最小的透明 PNG (1x1 像素)
  const minimalPNG = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG 签名
    0x00, 0x00, 0x00, 0x0D, // IHDR 长度
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x30, // 宽度 48
    0x00, 0x00, 0x00, 0x30, // 高度 48
    0x08, 0x06, 0x00, 0x00, 0x00, // 位深度、颜色类型等
    0x57, 0x02, 0xF9, 0x87, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND 长度
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);
  
  const icons = ['home', 'equipment', 'orders', 'chat', 'profile'];
  
  icons.forEach(icon => {
    fs.writeFileSync(`${icon}.png`, minimalPNG);
    fs.writeFileSync(`${icon}_active.png`, minimalPNG);
    console.log(`✓ 创建 ${icon} 占位图标`);
  });
}

// 主函数
try {
  require('canvas');
  generateIcons();
} catch (error) {
  createPlaceholderIcons();
}

console.log('\n图标生成完成！');
console.log('如需更精美的图标，请：');
console.log('1. 使用 SVG 文件到在线转换工具');
console.log('2. 或安装 canvas: npm install canvas');
