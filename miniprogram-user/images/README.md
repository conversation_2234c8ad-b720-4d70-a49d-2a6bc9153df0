# 图标文件说明

## TabBar 图标要求

为了完整显示TabBar图标，需要在此目录下添加以下图标文件：

### 必需的图标文件

#### 首页图标
- `home.png` - 首页未选中状态图标 (81x81px)
- `home_active.png` - 首页选中状态图标 (81x81px)

#### 设备图标
- `equipment.png` - 设备未选中状态图标 (81x81px)
- `equipment_active.png` - 设备选中状态图标 (81x81px)

#### 工单图标
- `orders.png` - 工单未选中状态图标 (81x81px)
- `orders_active.png` - 工单选中状态图标 (81x81px)

#### 消息图标
- `chat.png` - 消息未选中状态图标 (81x81px)
- `chat_active.png` - 消息选中状态图标 (81x81px)

#### 个人中心图标
- `profile.png` - 个人中心未选中状态图标 (81x81px)
- `profile_active.png` - 个人中心选中状态图标 (81x81px)

### 其他页面所需图标

#### 通用图标
- `logo.png` - 应用Logo (120x120px)
- `default-avatar.png` - 默认头像 (80x80px)
- `scan.png` - 扫描图标 (32x32px)
- `filter.png` - 筛选图标 (32x32px)
- `edit.png` - 编辑图标 (32x32px)
- `wechat.png` - 微信图标 (40x40px)

#### 角色图标
- `role-user.png` - 普通用户角色图标 (80x80px)
- `role-pi.png` - 课题组负责人角色图标 (80x80px)
- `role-engineer.png` - 工程师角色图标 (80x80px)
- `role-admin.png` - 管理员角色图标 (80x80px)
- `role-super-admin.png` - 超级管理员角色图标 (80x80px)
- `role-default.png` - 默认角色图标 (80x80px)

#### 功能图标
- `add-equipment.png` - 添加设备图标 (48x48px)
- `create-order.png` - 创建工单图标 (48x48px)
- `scan-qr.png` - 扫码图标 (48x48px)
- `view-orders.png` - 查看工单图标 (48x48px)
- `parts.png` - 配件图标 (48x48px)
- `users.png` - 用户管理图标 (48x48px)
- `stats.png` - 统计图标 (48x48px)
- `settings.png` - 设置图标 (48x48px)

#### 状态和空状态图标
- `empty-equipment.png` - 设备空状态图标 (200x200px)
- `empty-orders.png` - 工单空状态图标 (200x200px)
- `verified.png` - 已认证图标 (24x24px)
- `unverified.png` - 未认证图标 (24x24px)

#### 菜单图标
- `role-manage.png` - 角色管理图标 (32x32px)
- `verify.png` - 实名认证图标 (32x32px)
- `notifications.png` - 通知图标 (32x32px)
- `help.png` - 帮助图标 (32x32px)
- `about.png` - 关于图标 (32x32px)

## 图标规范

### 尺寸要求
- **TabBar图标**: 81x81px (推荐)
- **功能图标**: 32x32px 或 48x48px
- **头像图标**: 80x80px
- **Logo**: 120x120px
- **空状态图标**: 200x200px

### 格式要求
- 格式：PNG
- 背景：透明
- 颜色：
  - 未选中状态：#666666 或类似灰色
  - 选中状态：#1976D2 或主题蓝色

### 设计建议
- 图标风格保持一致
- 线条粗细统一
- 圆角统一
- 颜色符合应用主题

## 临时解决方案

在添加图标文件之前，可以：

1. **使用纯文字TabBar**（当前方案）
   - 暂时移除iconPath配置
   - 仅显示文字标签

2. **使用iconfont图标**
   - 集成iconfont字体文件
   - 通过CSS类名显示图标

3. **使用base64图标**
   - 将小图标转换为base64编码
   - 直接在CSS中使用

## 添加图标后的配置

添加图标文件后，需要在 `app.json` 中恢复图标配置：

```json
{
  "pagePath": "pages/home/<USER>",
  "text": "首页",
  "iconPath": "images/home.png",
  "selectedIconPath": "images/home_active.png"
}
```

## 图标资源推荐

- **Iconfont**: https://www.iconfont.cn/
- **Feather Icons**: https://feathericons.com/
- **Material Icons**: https://material.io/icons/
- **Ant Design Icons**: https://ant.design/components/icon/

建议使用统一的图标库以保持风格一致性。
