// 图标生成脚本 - 严肃商务风格的 SVG 图标

const fs = require('fs');
const path = require('path');

// SVG 图标定义
const icons = {
  // 首页图标
  home: {
    normal: `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9 22V12H15V22" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    active: `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" fill="#1976D2" stroke="#1976D2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9 22V12H15V22" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`
  },

  // 设备图标
  equipment: {
    normal: `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="#666666" stroke-width="2"/>
      <line x1="8" y1="21" x2="16" y2="21" stroke="#666666" stroke-width="2" stroke-linecap="round"/>
      <line x1="12" y1="17" x2="12" y2="21" stroke="#666666" stroke-width="2" stroke-linecap="round"/>
      <circle cx="7" cy="9" r="1" fill="#666666"/>
      <circle cx="12" cy="9" r="1" fill="#666666"/>
      <circle cx="17" cy="9" r="1" fill="#666666"/>
    </svg>`,
    active: `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="2" y="3" width="20" height="14" rx="2" ry="2" fill="#1976D2" stroke="#1976D2" stroke-width="2"/>
      <line x1="8" y1="21" x2="16" y2="21" stroke="#1976D2" stroke-width="2" stroke-linecap="round"/>
      <line x1="12" y1="17" x2="12" y2="21" stroke="#1976D2" stroke-width="2" stroke-linecap="round"/>
      <circle cx="7" cy="9" r="1" fill="#ffffff"/>
      <circle cx="12" cy="9" r="1" fill="#ffffff"/>
      <circle cx="17" cy="9" r="1" fill="#ffffff"/>
    </svg>`
  },

  // 工单图标
  orders: {
    normal: `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <polyline points="14,2 14,8 20,8" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <line x1="16" y1="13" x2="8" y2="13" stroke="#666666" stroke-width="2" stroke-linecap="round"/>
      <line x1="16" y1="17" x2="8" y2="17" stroke="#666666" stroke-width="2" stroke-linecap="round"/>
      <polyline points="10,9 9,9 8,9" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    active: `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" fill="#1976D2" stroke="#1976D2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <polyline points="14,2 14,8 20,8" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <line x1="16" y1="13" x2="8" y2="13" stroke="#ffffff" stroke-width="2" stroke-linecap="round"/>
      <line x1="16" y1="17" x2="8" y2="17" stroke="#ffffff" stroke-width="2" stroke-linecap="round"/>
      <polyline points="10,9 9,9 8,9" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`
  },

  // 消息图标
  chat: {
    normal: `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="8" cy="10" r="1" fill="#666666"/>
      <circle cx="12" cy="10" r="1" fill="#666666"/>
      <circle cx="16" cy="10" r="1" fill="#666666"/>
    </svg>`,
    active: `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" fill="#1976D2" stroke="#1976D2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="8" cy="10" r="1" fill="#ffffff"/>
      <circle cx="12" cy="10" r="1" fill="#ffffff"/>
      <circle cx="16" cy="10" r="1" fill="#ffffff"/>
    </svg>`
  },

  // 个人中心图标
  profile: {
    normal: `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="12" cy="7" r="4" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    active: `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" fill="#1976D2" stroke="#1976D2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="12" cy="7" r="4" fill="#1976D2" stroke="#1976D2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`
  }
};

// 生成 SVG 文件
function generateSVGFiles() {
  Object.keys(icons).forEach(iconName => {
    const normalSVG = icons[iconName].normal;
    const activeSVG = icons[iconName].active;

    // 写入普通状态 SVG
    fs.writeFileSync(path.join(__dirname, `${iconName}.svg`), normalSVG);

    // 写入激活状态 SVG
    fs.writeFileSync(path.join(__dirname, `${iconName}_active.svg`), activeSVG);
  });

  console.log('SVG 图标生成完成！');
  console.log('请使用在线工具将 SVG 转换为 PNG 格式：');
  console.log('1. 访问 https://convertio.co/svg-png/');
  console.log('2. 上传 SVG 文件');
  console.log('3. 设置尺寸为 48x48 像素');
  console.log('4. 下载 PNG 文件并替换现有图标');
}

// 执行生成
if (require.main === module) {
  generateSVGFiles();
}

// 简单的SVG到PNG转换（需要安装相关依赖）
// npm install canvas

try {
  const { createCanvas } = require('canvas');
  
  // 生成简单的图标
  function generateIcon(text, size, color, bgColor = 'transparent') {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // 设置背景
    if (bgColor !== 'transparent') {
      ctx.fillStyle = bgColor;
      ctx.fillRect(0, 0, size, size);
    }
    
    // 设置文字
    ctx.fillStyle = color;
    ctx.font = `${size * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, size / 2, size / 2);
    
    return canvas.toBuffer('image/png');
  }
  
  // TabBar图标配置
  const tabBarIcons = [
    { name: 'home', text: '🏠', activeText: '🏠' },
    { name: 'equipment', text: '⚙️', activeText: '⚙️' },
    { name: 'orders', text: '📋', activeText: '📋' },
    { name: 'chat', text: '💬', activeText: '💬' },
    { name: 'profile', text: '👤', activeText: '👤' }
  ];
  
  // 生成TabBar图标
  tabBarIcons.forEach(icon => {
    // 未选中状态 - 灰色
    const normalIcon = generateIcon(icon.text, 81, '#666666');
    fs.writeFileSync(path.join(__dirname, `${icon.name}.png`), normalIcon);
    
    // 选中状态 - 蓝色
    const activeIcon = generateIcon(icon.activeText, 81, '#1976D2');
    fs.writeFileSync(path.join(__dirname, `${icon.name}_active.png`), activeIcon);
  });
  
  // 生成其他常用图标
  const otherIcons = [
    { name: 'logo', text: '资', size: 120, color: '#1976D2' },
    { name: 'default-avatar', text: '👤', size: 80, color: '#666666' },
    { name: 'scan', text: '📷', size: 32, color: '#666666' },
    { name: 'filter', text: '🔍', size: 32, color: '#666666' },
    { name: 'edit', text: '✏️', size: 32, color: '#666666' },
    { name: 'wechat', text: '💬', size: 40, color: '#07C160' },
    { name: 'add-equipment', text: '➕', size: 48, color: '#1976D2' },
    { name: 'create-order', text: '📝', size: 48, color: '#1976D2' },
    { name: 'scan-qr', text: '📱', size: 48, color: '#1976D2' },
    { name: 'view-orders', text: '📋', size: 48, color: '#1976D2' }
  ];
  
  otherIcons.forEach(icon => {
    const iconBuffer = generateIcon(icon.text, icon.size, icon.color);
    fs.writeFileSync(path.join(__dirname, `${icon.name}.png`), iconBuffer);
  });
  
  console.log('图标生成完成！');
  console.log('生成的图标文件：');
  console.log('- TabBar图标：home.png, home_active.png, equipment.png, equipment_active.png, 等');
  console.log('- 功能图标：logo.png, default-avatar.png, scan.png, 等');
  
} catch (error) {
  console.log('Canvas模块未安装，无法生成图标。');
  console.log('请运行以下命令安装依赖：');
  console.log('npm install canvas');
  console.log('');
  console.log('或者手动添加图标文件到 images/ 目录。');
  console.log('参考 images/README.md 了解详细要求。');
}

// 如果没有canvas，创建一个简单的占位文件
if (!fs.existsSync(path.join(__dirname, 'home.png'))) {
  console.log('创建占位文件...');
  
  // 创建一个简单的占位文件（1x1像素的透明PNG）
  const placeholderPNG = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
    0x0B, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
    0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
    0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ]);
  
  const iconFiles = [
    'home.png', 'home_active.png',
    'equipment.png', 'equipment_active.png',
    'orders.png', 'orders_active.png',
    'chat.png', 'chat_active.png',
    'profile.png', 'profile_active.png',
    'logo.png', 'default-avatar.png', 'scan.png', 'filter.png',
    'edit.png', 'wechat.png', 'add-equipment.png', 'create-order.png',
    'scan-qr.png', 'view-orders.png'
  ];
  
  iconFiles.forEach(filename => {
    fs.writeFileSync(path.join(__dirname, filename), placeholderPNG);
  });
  
  console.log('占位图标文件已创建。');
  console.log('请替换为实际的图标文件以获得更好的显示效果。');
}
