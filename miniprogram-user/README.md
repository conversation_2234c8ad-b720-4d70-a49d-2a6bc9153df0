# 资管维小程序

## 开发环境配置

### 开发模式说明

当前小程序配置为开发模式，使用模拟数据而不是真实的后端API。

#### 开发模式配置

在 `app.js` 中：
```javascript
globalData: {
  // 开发模式标志 - 使用假数据
  isDevelopment: true
}
```

#### 切换到生产环境

当后端API准备就绪时，修改 `app.js`：

1. 将 `isDevelopment` 设置为 `false`
2. 更新 `baseUrl` 为正确的API地址
3. 在微信小程序后台配置合法域名

```javascript
globalData: {
  isDevelopment: false,  // 改为 false
  baseUrl: 'https://your-api-domain.com/api'  // 更新为实际API地址
}
```

### 项目配置

`project.config.json` 中已配置：
- `urlCheck: false` - 关闭域名校验（开发阶段）
- `checkSiteMap: false` - 关闭sitemap检查

### 模拟数据

当前支持的模拟API：
- `/auth/wechat-login` - 微信登录
- `/roles/switch` - 角色切换
- `/roles/my-roles` - 获取用户角色
- `/users/stats` - 用户统计数据
- `/engineers/stats` - 工程师统计数据
- `/orders/*` - 工单相关接口
- `/equipment` - 设备列表
- `/announcements` - 公告列表

### 开发注意事项

1. 所有API调用都会返回模拟数据
2. 网络请求有500ms的模拟延迟
3. 登录会自动创建测试用户
4. 角色切换功能正常工作

### 组件说明

#### role-switcher 组件

已创建的角色切换组件，支持：
- 紧凑模式和完整模式
- 角色选择弹窗
- 角色切换事件

使用方法：
```xml
<role-switcher 
  mode="compact" 
  custom-class="custom-style"
  bind:rolechange="onRoleChange">
</role-switcher>
```

### 调试建议

1. 在开发者工具中查看 Console 输出
2. 所有模拟API调用都会打印日志
3. 可以在 `getMockResponse` 方法中修改模拟数据
