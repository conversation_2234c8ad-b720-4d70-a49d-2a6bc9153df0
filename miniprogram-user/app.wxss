/* app.wxss */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* 通用样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-body {
  padding: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}

.card-subtitle {
  font-size: 28rpx;
  color: #666666;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #1976D2;
  color: #ffffff;
}

.btn-primary:hover {
  background-color: #1565C0;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666666;
}

.btn-success {
  background-color: #4CAF50;
  color: #ffffff;
}

.btn-warning {
  background-color: #FF9800;
  color: #ffffff;
}

.btn-danger {
  background-color: #F44336;
  color: #ffffff;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #1976D2;
  color: #1976D2;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 28rpx 56rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
  display: block;
}

.btn-disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #ffffff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1976D2;
  outline: none;
}

.form-textarea {
  min-height: 120rpx;
  resize: vertical;
}

.form-select {
  position: relative;
}

.form-select::after {
  content: '';
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #666666;
}

/* 列表样式 */
.list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background-color: #f9f9f9;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.list-item-subtitle {
  font-size: 26rpx;
  color: #666666;
}

.list-item-action {
  margin-left: 20rpx;
}

/* 状态标签 */
.status-tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
}

.status-pending {
  background-color: #FFF3E0;
  color: #F57C00;
}

.status-processing {
  background-color: #E3F2FD;
  color: #1976D2;
}

.status-completed {
  background-color: #E8F5E8;
  color: #388E3C;
}

.status-cancelled {
  background-color: #FFEBEE;
  color: #D32F2F;
}

/* 角色切换样式 */
.role-switcher {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.role-current {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.role-switch-btn {
  padding: 12rpx 24rpx;
  background: #1976D2;
  color: #ffffff;
  border-radius: 6rpx;
  font-size: 24rpx;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: #1976D2;
}

.text-success {
  color: #4CAF50;
}

.text-warning {
  color: #FF9800;
}

.text-danger {
  color: #F44336;
}

.text-muted {
  color: #666666;
}

.mt-10 {
  margin-top: 20rpx;
}

.mt-20 {
  margin-top: 40rpx;
}

.mb-10 {
  margin-bottom: 20rpx;
}

.mb-20 {
  margin-bottom: 40rpx;
}

.p-10 {
  padding: 20rpx;
}

.p-20 {
  padding: 40rpx;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.hidden {
  display: none;
}

/* 响应式 */
@media (max-width: 750rpx) {
  .container {
    padding: 15rpx;
  }
  
  .card-body {
    padding: 20rpx;
  }
}
