// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    refreshToken: null,
    currentRole: 'user',
    roles: [],
    isMultiRole: false,

    // 云托管配置
    cloudConfig: {
      env: 'prod-7g8u1mb18fbf5ba5', // 微信云托管环境ID
      serviceName: 'springboot-7eic' // 服务名称
    },

    // API配置 - 保留作为备用
    baseUrl: 'https://springboot-7eic-165879-6-1362145775.sh.run.tcloudbase.com/api',

    // 开发模式标志 - 控制是否使用Mock数据
    isDevelopment: false, // 设为true使用Mock数据，false使用真实API
    useCloudContainer: true // 是否使用云托管容器调用
  },

  onLaunch() {
    console.log('小程序启动');

    // 初始化云开发
    if (wx.cloud) {
      wx.cloud.init({
        // 可以不填env，因为我们使用云托管
        traceUser: true
      });
      console.log('云开发初始化成功');
    }

    this.checkLoginStatus();
  },

  // 环境切换工具函数
  switchToMockMode() {
    this.globalData.isDevelopment = true;
    console.log('已切换到Mock模式');
    wx.showToast({
      title: '已切换到Mock模式',
      icon: 'success'
    });
  },

  switchToRealMode() {
    this.globalData.isDevelopment = false;
    console.log('已切换到真实API模式');
    wx.showToast({
      title: '已切换到真实API',
      icon: 'success'
    });
  },

  // 切换云托管/HTTP调用方式
  switchToCloudContainer() {
    this.globalData.useCloudContainer = true;
    console.log('已切换到云托管容器调用');
    wx.showToast({
      title: '使用云托管调用',
      icon: 'success'
    });
  },

  switchToHttp() {
    this.globalData.useCloudContainer = false;
    console.log('已切换到HTTP调用');
    wx.showToast({
      title: '使用HTTP调用',
      icon: 'success'
    });
  },

  // 获取当前环境信息
  getEnvironmentInfo() {
    return {
      isDevelopment: this.globalData.isDevelopment,
      useCloudContainer: this.globalData.useCloudContainer,
      baseUrl: this.globalData.baseUrl,
      cloudConfig: this.globalData.cloudConfig
    };
  },

  onShow() {
    console.log('小程序显示');
    // 检查并更新tabBar
    this.updateTabBarByRole();
  },

  onHide() {
    console.log('小程序隐藏');
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
      this.globalData.currentRole = userInfo.currentRole || 'user';
      this.globalData.roles = userInfo.roles || ['user'];
      this.globalData.isMultiRole = userInfo.isMultiRole || false;
    }
  },

  // 微信登录
  async wechatLogin(userProfile) {
    try {
      // 获取微信授权码
      const loginRes = await this.wxLogin();

      // 调用后端登录接口
      const loginResult = await this.request({
        url: '/auth/wechat-login',
        method: 'POST',
        data: {
          code: loginRes.code,
          userInfo: userProfile.userInfo
        }
      });

      if (loginResult.success) {
        const { user, token, refreshToken, needRoleSelection } = loginResult.data;
        
        // 保存登录信息
        this.globalData.userInfo = user;
        this.globalData.token = token;
        this.globalData.refreshToken = refreshToken;
        this.globalData.currentRole = user.currentRole;
        this.globalData.roles = user.roles;
        this.globalData.isMultiRole = user.isMultiRole;

        // 本地存储
        wx.setStorageSync('token', token);
        wx.setStorageSync('refreshToken', refreshToken);
        wx.setStorageSync('userInfo', user);

        // 如果需要角色选择，跳转到角色选择页面
        if (needRoleSelection) {
          wx.navigateTo({
            url: '/pages/role-select/role-select'
          });
        }

        return { success: true, needRoleSelection };
      } else {
        throw new Error(loginResult.message);
      }
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      });
      return { success: false, error: error.message };
    }
  },

  // 微信登录
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },



  // 切换角色
  async switchRole(targetRole) {
    try {
      const result = await this.request({
        url: '/roles/switch',
        method: 'POST',
        data: { targetRole }
      });

      if (result.success) {
        const { currentRole, token } = result.data;

        // 更新全局数据
        this.globalData.currentRole = currentRole;
        this.globalData.token = token;

        // 更新用户信息
        const userInfo = { ...this.globalData.userInfo, currentRole };
        this.globalData.userInfo = userInfo;

        // 更新本地存储
        wx.setStorageSync('token', token);
        wx.setStorageSync('userInfo', userInfo);

        // 更新tabBar
        this.updateTabBarByRole();

        return { success: true };
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('角色切换失败:', error);
      wx.showToast({
        title: '角色切换失败',
        icon: 'error'
      });
      return { success: false, error: error.message };
    }
  },

  // 根据角色更新tabBar
  updateTabBarByRole() {
    const currentRole = this.globalData.currentRole || 'user';

    if (currentRole === 'engineer') {
      // 工程师角色：设置设备tab为灰色并提示
      this.setEquipmentTabDisabled();
    } else {
      // 其他角色：恢复设备tab正常状态
      this.setEquipmentTabEnabled();
    }
  },

  // 设置设备tab为禁用状态
  setEquipmentTabDisabled() {
    try {
      wx.setTabBarItem({
        index: 1,
        text: '设备',
        iconPath: 'images/equipment_disabled.png',
        selectedIconPath: 'images/equipment_disabled.png'
      });
    } catch (error) {
      console.error('设置设备tab禁用状态失败:', error);
    }
  },

  // 设置设备tab为启用状态
  setEquipmentTabEnabled() {
    try {
      wx.setTabBarItem({
        index: 1,
        text: '设备',
        iconPath: 'images/equipment.png',
        selectedIconPath: 'images/equipment_active.png'
      });
    } catch (error) {
      console.error('设置设备tab启用状态失败:', error);
    }
  },

  // 登出
  logout() {
    this.globalData.userInfo = null;
    this.globalData.token = null;
    this.globalData.refreshToken = null;
    this.globalData.currentRole = 'user';
    this.globalData.roles = [];
    this.globalData.isMultiRole = false;

    wx.removeStorageSync('token');
    wx.removeStorageSync('refreshToken');
    wx.removeStorageSync('userInfo');

    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    });
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      const { url, method = 'GET', data = {}, needAuth = true } = options;

      // 开发模式：使用假数据
      if (this.globalData.isDevelopment) {
        setTimeout(() => {
          const mockResponse = this.getMockResponse(url, method, data);
          resolve(mockResponse);
        }, 500); // 模拟网络延迟
        return;
      }

      // 使用云托管容器调用
      if (this.globalData.useCloudContainer && wx.cloud && wx.cloud.callContainer) {
        this.callCloudContainer(options).then(resolve).catch(reject);
        return;
      }

      // 传统HTTP请求
      this.callHttp(options).then(resolve).catch(reject);
    });
  },

  // 云托管容器调用
  async callCloudContainer(options) {
    const { url, method = 'GET', data = {}, needAuth = true } = options;

    const header = {
      'Content-Type': 'application/json'
    };

    // 添加认证头
    if (needAuth && this.globalData.token) {
      header.Authorization = `Bearer ${this.globalData.token}`;
    }

    try {
      const res = await wx.cloud.callContainer({
        config: {
          env: this.globalData.cloudConfig.env
        },
        path: '/api' + url, // 添加/api前缀
        method: method.toUpperCase(),
        header: {
          ...header,
          'X-WX-SERVICE': this.globalData.cloudConfig.serviceName
        },
        data: method.toUpperCase() === 'GET' ? undefined : data,
        timeout: 15000 // 设置15秒超时（最大值）
      });

      if (res.statusCode === 200) {
        return res.data;
      } else if (res.statusCode === 401) {
        // Token过期，尝试刷新
        try {
          await this.refreshToken();
          // 重新发起请求
          return await this.callCloudContainer(options);
        } catch (error) {
          // 刷新失败，跳转登录
          this.logout();
          throw new Error('登录已过期');
        }
      } else {
        throw new Error(res.data?.message || '请求失败');
      }
    } catch (error) {
      console.error('云托管请求失败:', error);

      // 如果是超时错误，尝试降级到HTTP请求
      if (error.errCode === 102002) {
        console.log('云托管请求超时，尝试降级到HTTP请求');
        try {
          return await this.callHttp(options);
        } catch (httpError) {
          console.error('HTTP请求也失败:', httpError);
          throw new Error('服务暂时不可用，请稍后重试');
        }
      }

      throw error;
    }
  },

  // 传统HTTP请求
  async callHttp(options) {
    const { url, method = 'GET', data = {}, needAuth = true } = options;

    const header = {
      'Content-Type': 'application/json'
    };

    // 添加认证头
    if (needAuth && this.globalData.token) {
      header.Authorization = `Bearer ${this.globalData.token}`;
    }

    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + url,
        method,
        data,
        header,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else if (res.statusCode === 401) {
            // Token过期，尝试刷新
            this.refreshToken().then(() => {
              // 重新发起请求
              this.callHttp(options).then(resolve).catch(reject);
            }).catch(() => {
              // 刷新失败，跳转登录
              this.logout();
              reject(new Error('登录已过期'));
            });
          } else {
            reject(new Error(res.data?.message || '请求失败'));
          }
        },
        fail: (error) => {
          console.error('HTTP请求失败:', error);
          reject(error);
        }
      });
    });
  },

  // 模拟API响应
  getMockResponse(url, method, data) {
    console.log('Mock API:', method, url, data);

    // 登录接口
    if (url === '/auth/wechat-login' && method === 'POST') {
      return {
        success: true,
        message: '登录成功',
        data: {
          user: {
            id: 1,
            username: data.userInfo?.nickName || '测试用户',
            avatar: data.userInfo?.avatarUrl || '/images/default-avatar.png',
            phone: '13800138000',
            email: '<EMAIL>',
            isVerified: true,
            currentRole: 'user',
            roles: ['user', 'pi'],
            isMultiRole: true,
            department: '计算机学院',
            researchGroup: '人工智能实验室'
          },
          token: 'mock_token_' + Date.now(),
          refreshToken: 'mock_refresh_token_' + Date.now(),
          needRoleSelection: true
        }
      };
    }

    // 角色切换接口
    if (url === '/roles/switch' && method === 'POST') {
      return {
        success: true,
        message: '角色切换成功',
        data: {
          currentRole: data.targetRole,
          token: 'mock_token_' + Date.now()
        }
      };
    }

    // 获取用户角色列表
    if (url === '/roles/my-roles' && method === 'GET') {
      return {
        success: true,
        data: {
          roles: [
            {
              role: 'user',
              roleName: '普通用户',
              department: '计算机学院',
              isActive: true
            },
            {
              role: 'pi',
              roleName: 'PI负责人',
              department: '计算机学院',
              isActive: true
            },
            {
              role: 'engineer',
              roleName: '维修工程师',
              department: '设备维修部',
              isActive: false
            }
          ],
          currentRole: this.globalData.currentRole
        }
      };
    }

    // 刷新Token接口
    if (url === '/auth/refresh-token' && method === 'POST') {
      return {
        success: true,
        message: 'Token刷新成功',
        data: {
          token: 'mock_token_' + Date.now(),
          refreshToken: 'mock_refresh_token_' + Date.now()
        }
      };
    }

    // 首页统计数据
    if ((url === '/users/stats' || url === '/engineers/stats') && method === 'GET') {
      return {
        success: true,
        data: {
          equipmentCount: 25,
          orderCount: 12,
          pendingOrders: 3,
          completedOrders: 8,
          assignedOrders: 5,
          inProgressOrders: 2,
          completedToday: 1
        }
      };
    }

    // 工单列表
    if ((url === '/orders/my' || url === '/orders/assigned' || url === '/orders') && method === 'GET') {
      const allOrders = [
        {
          id: 1,
          orderNumber: 'WO202401001',
          title: '激光打印机卡纸故障',
          description: '打印机经常卡纸，特别是打印双面文档时',
          status: 'pending',
          priority: 'high',
          faultType: 'hardware',
          createdAt: '2024-01-15 10:30:00',
          equipment: {
            id: 1,
            name: 'HP LaserJet Pro',
            model: 'M404n',
            location: '实验室A-101'
          },
          creator: {
            id: 1,
            username: '张三'
          },
          assignee: null
        },
        {
          id: 2,
          orderNumber: 'WO202401002',
          title: '显微镜校准检测',
          description: '显微镜成像不清晰，需要进行校准',
          status: 'assigned',
          priority: 'medium',
          faultType: 'calibration',
          createdAt: '2024-01-14 14:20:00',
          equipment: {
            id: 2,
            name: '光学显微镜',
            model: 'BX53',
            location: '实验室B-203'
          },
          creator: {
            id: 2,
            username: '李四'
          },
          assignee: {
            id: 3,
            username: '王工程师'
          }
        },
        {
          id: 3,
          orderNumber: 'WO202401003',
          title: '离心机异响维修',
          description: '离心机运行时有异常响声，疑似轴承问题',
          status: 'in_progress',
          priority: 'urgent',
          faultType: 'hardware',
          createdAt: '2024-01-13 09:15:00',
          equipment: {
            id: 3,
            name: '高速离心机',
            model: 'CR22N',
            location: '实验室C-305'
          },
          creator: {
            id: 3,
            username: '赵老师'
          },
          assignee: {
            id: 3,
            username: '王工程师'
          }
        },
        {
          id: 4,
          orderNumber: 'WO202401004',
          title: '电子天平校准',
          description: '定期校准，确保测量精度',
          status: 'completed',
          priority: 'low',
          faultType: 'maintenance',
          createdAt: '2024-01-12 16:45:00',
          equipment: {
            id: 4,
            name: '电子天平',
            model: 'XS205',
            location: '实验室A-102'
          },
          creator: {
            id: 4,
            username: '钱老师'
          },
          assignee: {
            id: 4,
            username: '李工程师'
          }
        },
        {
          id: 5,
          orderNumber: 'WO202401005',
          title: 'PCR仪温度异常',
          description: 'PCR仪温度控制不稳定，影响实验结果',
          status: 'pending',
          priority: 'high',
          faultType: 'hardware',
          createdAt: '2024-01-11 11:20:00',
          equipment: {
            id: 5,
            name: 'PCR仪',
            model: 'T100',
            location: '实验室D-401'
          },
          creator: {
            id: 5,
            username: '孙老师'
          },
          assignee: null
        }
      ];

      // 根据查询参数过滤
      let filteredOrders = allOrders;

      // 状态过滤
      if (data.status) {
        filteredOrders = filteredOrders.filter(item => item.status === data.status);
      }

      // 根据不同角色返回不同数据
      const currentRole = this.globalData.currentRole || 'user';
      if (url === '/orders/my') {
        // 用户自己的工单
        filteredOrders = filteredOrders.filter(item =>
          item.creator.username === '张三' // 模拟当前用户
        );
      } else if (url === '/orders/assigned') {
        // 工程师分配的工单
        filteredOrders = filteredOrders.filter(item =>
          item.assignee && item.assignee.username === '王工程师' // 模拟当前工程师
        );
      }

      // 分页
      const page = parseInt(data.page) || 1;
      const limit = parseInt(data.limit) || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedList = filteredOrders.slice(startIndex, endIndex);

      // 统计数据
      const stats = {
        total: filteredOrders.length,
        pending: filteredOrders.filter(item => item.status === 'pending').length,
        assigned: filteredOrders.filter(item => item.status === 'assigned').length,
        in_progress: filteredOrders.filter(item => item.status === 'in_progress').length,
        completed: filteredOrders.filter(item => item.status === 'completed').length,
        cancelled: filteredOrders.filter(item => item.status === 'cancelled').length
      };

      return {
        success: true,
        data: {
          list: paginatedList,
          pagination: {
            totalPages: Math.ceil(filteredOrders.length / limit),
            currentPage: page,
            total: filteredOrders.length
          },
          stats
        }
      };
    }

    // 创建工单
    if (url === '/orders' && method === 'POST') {
      return {
        success: true,
        message: '工单创建成功',
        data: {
          id: Date.now(),
          orderNumber: 'WO' + new Date().getFullYear() +
                      String(new Date().getMonth() + 1).padStart(2, '0') +
                      String(Date.now()).slice(-3),
          ...data,
          status: 'pending',
          createdAt: new Date().toISOString()
        }
      };
    }

    // 工单详情
    if (url.startsWith('/orders/') && method === 'GET' && !url.includes('/history')) {
      const orderId = url.split('/')[2];
      return {
        success: true,
        data: {
          id: orderId,
          orderNumber: 'WO202401001',
          title: '激光打印机卡纸故障',
          description: '打印机经常卡纸，特别是打印双面文档时。已经尝试清理纸道，但问题依然存在。影响日常办公效率。',
          status: 'in_progress',
          priority: 'high',
          faultType: 'hardware',
          urgentLevel: 'urgent',
          contactPhone: '13800138000',
          expectedDate: '2024-01-20',
          createdAt: '2024-01-15 10:30:00',
          equipment: {
            id: 1,
            name: 'HP LaserJet Pro',
            model: 'M404n',
            location: '实验室A-101'
          },
          creator: {
            id: 1,
            username: '张三'
          },
          assignee: {
            id: 2,
            username: '李工程师'
          },
          images: [
            '/images/fault1.jpg',
            '/images/fault2.jpg'
          ]
        }
      };
    }

    // 工单状态历史
    if (url.includes('/orders/') && url.includes('/history') && method === 'GET') {
      return {
        success: true,
        data: {
          list: [
            {
              id: 1,
              action: '工单已创建',
              operator: '张三',
              createdAt: '2024-01-15 10:30:00',
              remark: '用户提交维修申请'
            },
            {
              id: 2,
              action: '工单已接受',
              operator: '李工程师',
              createdAt: '2024-01-15 14:20:00',
              remark: '已安排维修人员'
            },
            {
              id: 3,
              action: '开始处理',
              operator: '李工程师',
              createdAt: '2024-01-16 09:00:00',
              remark: '现场检查设备状况'
            }
          ]
        }
      };
    }

    // 更新工单状态
    if (url.includes('/orders/') && url.includes('/status') && method === 'PUT') {
      return {
        success: true,
        message: '状态更新成功',
        data: {
          status: data.status,
          updatedAt: new Date().toISOString()
        }
      };
    }

    // 接受工单
    if (url.includes('/orders/') && url.includes('/accept') && method === 'POST') {
      return {
        success: true,
        message: '工单接受成功',
        data: {
          status: 'in_progress',
          acceptedAt: new Date().toISOString()
        }
      };
    }

    // 完成工单
    if (url.includes('/orders/') && url.includes('/complete') && method === 'POST') {
      return {
        success: true,
        message: '工单完成成功',
        data: {
          status: 'completed',
          completedAt: new Date().toISOString()
        }
      };
    }

    // 通过标识符获取设备信息
    if (url === '/equipment/getByIdentifier' && method === 'GET') {
      const { identifier, type } = data;

      console.log(`模拟API: 通过${type}获取设备信息`, identifier);

      // 模拟设备数据库
      const mockEquipmentDatabase = {
        'EQ001': {
          id: 'EQ001',
          name: '激光打印机',
          model: 'HP LaserJet Pro M404n',
          serialNumber: 'CN12345678',
          location: '办公室A-101',
          status: 'normal',
          description: 'HP激光打印机，黑白打印',
          purchaseDate: '2023-01-15',
          warrantyExpiry: '2026-01-15',
          brand: 'HP'
        },
        'CN12345678': {
          id: 'EQ001',
          name: '激光打印机',
          model: 'HP LaserJet Pro M404n',
          serialNumber: 'CN12345678',
          location: '办公室A-101',
          status: 'normal',
          description: 'HP激光打印机，黑白打印',
          purchaseDate: '2023-01-15',
          warrantyExpiry: '2026-01-15',
          brand: 'HP'
        },
        'EQ002': {
          id: 'EQ002',
          name: '投影仪',
          model: 'Epson EB-X41',
          serialNumber: 'EP87654321',
          location: '会议室B-201',
          status: 'maintenance',
          description: 'Epson投影仪，支持1080p',
          purchaseDate: '2023-03-20',
          warrantyExpiry: '2026-03-20',
          brand: 'Epson'
        },
        'EP87654321': {
          id: 'EQ002',
          name: '投影仪',
          model: 'Epson EB-X41',
          serialNumber: 'EP87654321',
          location: '会议室B-201',
          status: 'maintenance',
          description: 'Epson投影仪，支持1080p',
          purchaseDate: '2023-03-20',
          warrantyExpiry: '2026-03-20',
          brand: 'Epson'
        },
        'EQ003': {
          id: 'EQ003',
          name: '高速离心机',
          model: 'Hitachi CR22N',
          serialNumber: 'HT98765432',
          location: '实验室C-305',
          status: 'fault',
          description: '高速离心机，最高转速22000rpm',
          purchaseDate: '2022-08-10',
          warrantyExpiry: '2025-08-10',
          brand: 'Hitachi'
        },
        'HT98765432': {
          id: 'EQ003',
          name: '高速离心机',
          model: 'Hitachi CR22N',
          serialNumber: 'HT98765432',
          location: '实验室C-305',
          status: 'fault',
          description: '高速离心机，最高转速22000rpm',
          purchaseDate: '2022-08-10',
          warrantyExpiry: '2025-08-10',
          brand: 'Hitachi'
        }
      };

      const equipment = mockEquipmentDatabase[identifier];

      if (equipment) {
        return {
          success: true,
          data: equipment,
          message: '设备信息获取成功'
        };
      } else {
        return {
          success: false,
          data: null,
          message: '设备不存在或已被删除'
        };
      }
    }

    // 设备列表
    if (url === '/equipment' && method === 'GET') {
      const allEquipment = [
        {
          id: 1,
          name: 'HP LaserJet Pro',
          model: 'M404n',
          serialNumber: 'HP001234',
          maintenanceCode: 'EQ001',
          brand: 'HP',
          status: 'normal',
          location: '实验室A-101',
          purchaseDate: '2023-06-15',
          warrantyExpiry: '2025-06-15',
          updatedAt: '2024-01-15 14:30:00'
        },
        {
          id: 2,
          name: '光学显微镜',
          model: 'BX53',
          serialNumber: 'OLY5678',
          maintenanceCode: 'EQ002',
          brand: 'Olympus',
          status: 'maintenance',
          location: '实验室B-203',
          purchaseDate: '2022-03-20',
          warrantyExpiry: '2024-03-20',
          updatedAt: '2024-01-14 09:15:00'
        },
        {
          id: 3,
          name: '高速离心机',
          model: 'CR22N',
          serialNumber: 'HT9876',
          maintenanceCode: 'EQ003',
          brand: 'Hitachi',
          status: 'fault',
          location: '实验室C-305',
          purchaseDate: '2021-11-10',
          warrantyExpiry: '2023-11-10',
          updatedAt: '2024-01-13 16:45:00'
        },
        {
          id: 4,
          name: '电子天平',
          model: 'XS205',
          serialNumber: 'MT2468',
          maintenanceCode: 'EQ004',
          brand: 'Mettler Toledo',
          status: 'normal',
          location: '实验室A-102',
          purchaseDate: '2023-08-20',
          warrantyExpiry: '2025-08-20',
          updatedAt: '2024-01-12 11:20:00'
        },
        {
          id: 5,
          name: 'PCR仪',
          model: 'T100',
          serialNumber: 'BR1357',
          maintenanceCode: 'EQ005',
          brand: 'Bio-Rad',
          status: 'normal',
          location: '实验室D-401',
          purchaseDate: '2022-05-15',
          warrantyExpiry: '2024-05-15',
          updatedAt: '2024-01-11 08:30:00'
        }
      ];

      // 根据查询参数过滤
      let filteredEquipment = allEquipment;

      // 状态过滤
      if (data.status) {
        filteredEquipment = filteredEquipment.filter(item => item.status === data.status);
      }

      // 关键词搜索
      if (data.keyword) {
        const keyword = data.keyword.toLowerCase();
        filteredEquipment = filteredEquipment.filter(item =>
          item.name.toLowerCase().includes(keyword) ||
          item.model.toLowerCase().includes(keyword) ||
          item.serialNumber.toLowerCase().includes(keyword) ||
          item.maintenanceCode.toLowerCase().includes(keyword)
        );
      }

      // 分页
      const page = parseInt(data.page) || 1;
      const limit = parseInt(data.limit) || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedList = filteredEquipment.slice(startIndex, endIndex);

      // 统计数据
      const stats = {
        total: allEquipment.length,
        normal: allEquipment.filter(item => item.status === 'normal').length,
        maintenance: allEquipment.filter(item => item.status === 'maintenance').length,
        fault: allEquipment.filter(item => item.status === 'fault').length,
        retired: allEquipment.filter(item => item.status === 'retired').length
      };

      return {
        success: true,
        data: {
          list: paginatedList,
          pagination: {
            totalPages: Math.ceil(filteredEquipment.length / limit),
            currentPage: page,
            total: filteredEquipment.length
          },
          stats
        }
      };
    }

    // 添加设备
    if (url === '/equipment' && method === 'POST') {
      return {
        success: true,
        message: '设备添加成功',
        data: {
          id: Date.now(),
          ...data,
          status: 'normal',
          createdAt: new Date().toISOString()
        }
      };
    }

    // 设备详情
    if (url.startsWith('/equipment/') && method === 'GET' && !url.includes('/orders')) {
      const equipmentId = url.split('/')[2];
      return {
        success: true,
        data: {
          id: equipmentId,
          name: 'HP LaserJet Pro',
          model: 'M404n',
          serialNumber: 'HP001234',
          manufacturer: 'HP Inc.',
          status: 'normal',
          location: '实验室A-101',
          department: '计算机学院',
          researchGroup: '人工智能实验室',
          category: 'computer',
          purchaseDate: '2023-06-15',
          warrantyExpiry: '2025-06-15',
          price: '3500',
          supplier: '北京科技有限公司',
          description: '高性能激光打印机，适用于办公文档打印',
          specifications: '打印速度：38页/分钟\n分辨率：1200x1200dpi\n内存：256MB\n接口：USB 2.0, 以太网',
          createdAt: '2023-06-15 10:30:00'
        }
      };
    }

    // 设备维修历史
    if (url.includes('/equipment/') && url.includes('/orders') && method === 'GET') {
      return {
        success: true,
        data: {
          list: [
            {
              id: 1,
              orderNumber: 'WO202401001',
              title: '打印机卡纸维修',
              status: 'completed',
              statusText: '已完成',
              createdAt: '2024-01-10 14:30:00'
            },
            {
              id: 2,
              orderNumber: 'WO202312015',
              title: '定期保养',
              status: 'completed',
              statusText: '已完成',
              createdAt: '2023-12-15 09:00:00'
            }
          ]
        }
      };
    }

    // 公告列表
    if (url === '/announcements' && method === 'GET') {
      const mockAnnouncements = [
        {
          id: 1,
          title: '系统维护通知',
          content: '系统将于本周六进行维护升级',
          createdAt: '2024-01-15 09:00:00',
          isImportant: true
        },
        {
          id: 2,
          title: '新功能上线',
          content: '设备管理模块新增批量导入功能',
          createdAt: '2024-01-14 16:30:00',
          isImportant: false
        }
      ];

      return {
        success: true,
        data: {
          list: mockAnnouncements,
          pagination: { totalPages: 1, currentPage: 1, total: 2 }
        }
      };
    }

    // 获取用户信息
    if (url === '/users/profile' && method === 'GET') {
      return {
        success: true,
        data: {
          id: 1,
          username: '张三',
          phone: '13800138000',
          email: '<EMAIL>',
          avatar: '/images/default-avatar.png',
          department: '计算机学院',
          researchGroup: '人工智能实验室',
          isVerified: false,
          joinDate: '2023-09-01',
          lastLoginAt: '2024-01-15 14:30:00'
        }
      };
    }

    // 更新用户信息
    if (url === '/users/profile' && method === 'PUT') {
      return {
        success: true,
        message: '信息更新成功',
        data: {
          ...data,
          updatedAt: new Date().toISOString()
        }
      };
    }

    // 获取通知数量
    if (url === '/notifications/unread-count' && method === 'GET') {
      return {
        success: true,
        data: {
          count: Math.floor(Math.random() * 10) // 随机生成0-9的未读数量
        }
      };
    }

    // 获取认证状态
    if (url === '/users/verify-status' && method === 'GET') {
      return {
        success: true,
        data: {
          isVerified: false,
          status: 'pending',
          realName: '',
          idCard: ''
        }
      };
    }

    // 提交实名认证
    if (url === '/users/verify' && method === 'POST') {
      return {
        success: true,
        message: '认证申请提交成功',
        data: {
          status: 'processing',
          submittedAt: new Date().toISOString()
        }
      };
    }

    // 消息列表（聊天页面）
    if (url === '/messages' && method === 'GET') {
      return {
        success: true,
        data: {
          list: [
            {
              id: 1,
              order: {
                id: 1,
                order_number: 'WO202401001',
                title: '空调制冷效果差',
                status: 'in_progress',
                reporter: { id: 1, username: '张三', real_name: '张三' },
                engineer: { id: 2, username: '李工', real_name: '李工程师' }
              },
              lastMessage: {
                content: '设备已检查完毕，需要更换压缩机',
                created_at: '2024-01-15 14:30:00',
                sender: { id: 2, username: '李工', real_name: '李工程师' }
              },
              unreadCount: 2
            },
            {
              id: 2,
              order: {
                id: 2,
                order_number: 'WO202401002',
                title: '打印机无法正常工作',
                status: 'assigned',
                reporter: { id: 3, username: '王五', real_name: '王五' },
                engineer: { id: 4, username: '赵工', real_name: '赵工程师' }
              },
              lastMessage: {
                content: '我明天上午过去检查',
                created_at: '2024-01-15 16:45:00',
                sender: { id: 4, username: '赵工', real_name: '赵工程师' }
              },
              unreadCount: 0
            },
            {
              id: 3,
              order: {
                id: 3,
                order_number: 'WO202401003',
                title: '投影仪显示异常',
                status: 'pending',
                reporter: { id: 5, username: '陈六', real_name: '陈六' },
                engineer: null
              },
              lastMessage: {
                content: '投影仪出现花屏现象，请尽快处理',
                created_at: '2024-01-15 10:20:00',
                sender: { id: 5, username: '陈六', real_name: '陈六' }
              },
              unreadCount: 1
            },
            {
              id: 4,
              order: {
                id: 4,
                order_number: 'WO202401004',
                title: '网络设备故障',
                status: 'completed',
                reporter: { id: 6, username: '刘七', real_name: '刘七' },
                engineer: { id: 2, username: '李工', real_name: '李工程师' }
              },
              lastMessage: {
                content: '问题已解决，感谢您的配合',
                created_at: '2024-01-14 17:30:00',
                sender: { id: 2, username: '李工', real_name: '李工程师' }
              },
              unreadCount: 0
            }
          ]
        }
      };
    }

    // 发送消息
    if (url === '/messages' && method === 'POST') {
      // 模拟自动回复
      const autoReplies = [
        '收到您的消息，我们会尽快回复。',
        '感谢您的反馈，我们正在处理中。',
        '如需紧急帮助，请拨打客服热线：400-123-4567',
        '您的问题已记录，技术人员会在24小时内联系您。'
      ];

      const userMessage = {
        id: Date.now(),
        content: data.content,
        type: data.type,
        sender: 'user',
        senderName: '我',
        avatar: '/images/default-avatar.png',
        createdAt: new Date().toISOString()
      };

      // 模拟系统自动回复（延迟1秒）
      setTimeout(() => {
        const replyContent = autoReplies[Math.floor(Math.random() * autoReplies.length)];
        // 这里在实际应用中会通过WebSocket或其他方式推送消息
        console.log('Auto reply:', replyContent);
      }, 1000);

      return {
        success: true,
        data: userMessage
      };
    }

    // 用户统计信息
    if (url === '/users/stats' && method === 'GET') {
      return {
        success: true,
        data: {
          myOrders: 12,
          myEquipment: 8,
          unreadMessages: 3
        }
      };
    }

    // 用户个人信息
    if (url === '/users/profile' && method === 'GET') {
      return {
        success: true,
        data: {
          id: 1,
          username: 'demo_user',
          realName: '张三',
          email: '<EMAIL>',
          phone: '13800138000',
          department: '计算机科学与技术学院',
          researchGroup: '人工智能实验室',
          avatar: '/images/default-avatar.png',
          isVerified: true,
          createdAt: '2024-01-01 10:00:00',
          updatedAt: '2024-01-15 16:30:00'
        }
      };
    }

    // 聊天消息列表
    if (url === '/chat/messages' && method === 'GET') {
      const orderId = data.order_id;
      return {
        success: true,
        data: {
          list: [
            {
              id: 1,
              order_id: orderId,
              sender_id: 1,
              sender: {
                id: 1,
                username: 'zhangsan',
                real_name: '张三',
                avatar: '/images/default-avatar.png'
              },
              message_type: 'text',
              content: '您好，我的空调制冷效果很差，请帮忙检查一下。',
              created_at: '2024-01-15 10:30:00',
              is_read: true
            },
            {
              id: 2,
              order_id: orderId,
              sender_id: 2,
              sender: {
                id: 2,
                username: 'ligong',
                real_name: '李工程师',
                avatar: '/images/default-avatar.png'
              },
              message_type: 'text',
              content: '好的，我会尽快安排时间过去检查。请问您方便的时间是？',
              created_at: '2024-01-15 11:00:00',
              is_read: true
            },
            {
              id: 3,
              order_id: orderId,
              sender_id: 1,
              sender: {
                id: 1,
                username: 'zhangsan',
                real_name: '张三',
                avatar: '/images/default-avatar.png'
              },
              message_type: 'text',
              content: '明天下午2点以后都可以，谢谢！',
              created_at: '2024-01-15 11:15:00',
              is_read: true
            },
            {
              id: 4,
              order_id: orderId,
              sender_id: 2,
              sender: {
                id: 2,
                username: 'ligong',
                real_name: '李工程师',
                avatar: '/images/default-avatar.png'
              },
              message_type: 'text',
              content: '设备已检查完毕，需要更换压缩机。预计明天可以完成维修。',
              created_at: '2024-01-15 14:30:00',
              is_read: false
            }
          ],
          total: 4,
          page: 1,
          limit: 50,
          totalPages: 1
        }
      };
    }

    // 发送聊天消息
    if (url === '/chat/send' && method === 'POST') {
      return {
        success: true,
        data: {
          id: Date.now(),
          order_id: data.order_id,
          sender_id: this.globalData.userInfo?.id || 1,
          sender: {
            id: this.globalData.userInfo?.id || 1,
            username: this.globalData.userInfo?.username || 'current_user',
            real_name: this.globalData.userInfo?.realName || '当前用户',
            avatar: this.globalData.userInfo?.avatar || '/images/default-avatar.png'
          },
          message_type: data.message_type,
          content: data.content,
          file_name: data.file_name,
          created_at: new Date().toISOString(),
          is_read: true
        }
      };
    }

    // 标记消息已读
    if (url === '/chat/mark-read' && method === 'POST') {
      return {
        success: true,
        data: { message: '标记成功' }
      };
    }

    // 默认响应
    return {
      success: false,
      message: '接口未实现',
      data: null
    };
  },

  // 刷新Token
  async refreshToken() {
    try {
      const result = await this.request({
        url: '/auth/refresh-token',
        method: 'POST',
        data: {
          refreshToken: this.globalData.refreshToken
        },
        needAuth: false
      });

      if (result.success) {
        const { token, refreshToken } = result.data;
        this.globalData.token = token;
        this.globalData.refreshToken = refreshToken;

        wx.setStorageSync('token', token);
        wx.setStorageSync('refreshToken', refreshToken);

        return { success: true };
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('刷新Token失败:', error);
      throw error;
    }
  },

  // 显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  // 隐藏加载中
  hideLoading() {
    wx.hideLoading();
  },

  // 显示提示
  showToast(title, icon = 'success') {
    wx.showToast({
      title,
      icon,
      duration: 2000
    });
  }
});
