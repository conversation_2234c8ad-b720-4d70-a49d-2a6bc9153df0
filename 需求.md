# "资管维"平台软件需求规格说明书

## 1. 项目概述

### 1.1 项目名称
资管维设备维修管理平台

### 1.2 项目背景
本平台旨在为科研院所、高校等机构提供专业的设备维修管理服务，实现设备报修、维修调度、订单跟踪等全流程数字化管理。

### 1.3 用户角色
- **C端用户**：设备使用人员，负责设备报修和跟踪维修进度
- **B端管理员**：平台管理人员，负责工单调度、用户管理、数据统计等
- **工程师**：维修服务人员，负责接单和执行维修任务
- **PI（项目负责人）**：具有最高权限的管理员，可修改所有权限配置

## 2. 功能需求

### 2.1 C端用户功能模块

#### 2.1.1 报修服务
**功能描述**：用户可提交设备维修申请
**具体功能**：
- 设备信息录入
    - 设备名称（必填，文本输入）
    - 品牌（必填，下拉选择+手动输入）
    - 型号（必填，文本输入）
    - 序列号（选填，文本输入）
    - 维护编号（系统自动生成，全平台唯一）
    - 启用日期（必填，日期选择器）
    - 保修状态（必选，单选：在保/过保/不详）
    - 设备图片（选填，支持上传多张图片，单张不超过5MB）
- 故障现象描述（必填，文本输入，限制1000字符）
- 紧急程度选择（必选，单选：紧急/一般/非紧急）

#### 2.1.2 订单管理
**功能描述**：管理个人维修订单
**具体功能**：
- 历史订单查询
    - 订单状态：待确认/待分配/已分配/维修中/待验收/待结算/结算中/已完成/已取消
    - 支持按时间、状态、设备名称筛选
    - 分页显示，每页10条记录
- 订单操作
    - 取消订单（仅限"待确认"和"待分配"状态）
    - 修改订单（仅限"待确认"状态，可修改故障描述和紧急程度）
- 服务评价
    - 评价对象：工程师服务质量
    - 评价等级：非常满意(5分)/满意(4分)/一般(3分)/不满意(2分)/很不满意(1分)
    - 评价内容：文字反馈（选填，限制500字符）

#### 2.1.3 实时沟通
**功能描述**：与工程师和客服进行沟通
**具体功能**：
- 在线聊天
    - 文字消息发送接收
    - 语音消息发送接收（限制60秒）
    - 图片发送（限制5MB）
    - 消息历史记录保存
- 客服热线
    - 一键拨打客服电话
    - 显示客服工作时间：工作日9:00-18:00

#### 2.1.4 通知与提醒
**功能描述**：接收订单状态更新和系统通知
**具体功能**：
- 推送通知
    - 订单状态变更通知
    - 工程师接单通知
    - 预约上门时间通知
    - 维修完成通知
- 系统公告
    - 节假日服务调整通知
    - 平台维护通知

#### 2.1.5 设备档案
**功能描述**：查看设备维修历史
**具体功能**：
- 设备列表展示
- 维修记录查询
- 设备二维码生成和查看
- 维修报告下载

#### 2.1.6 其他功能
**具体功能**：
- 批量设备导入（Excel模板）
- 手动添加设备
- 全局搜索功能（支持设备名称、型号、维护编号搜索）
- 设备二维码生成（包含设备基本信息，仅PI和管理员可查看详细信息）

#### 2.1.7 个人中心
**具体功能**：
- 个人信息管理（姓名、手机、邮箱、所属单位、课题组）
- 密码修改
- 账号注销申请

### 2.2 B端管理员功能模块

#### 2.2.1 用户与权限管理
**功能描述**：管理平台用户和权限分配
**具体功能**：
- 用户管理
    - 用户信息查看（标记为"高级保密"的信息仅PI可查看）
    - 账号启用/禁用
    - 密码重置
    - 用户实名认证审核
- 权限管理
    - 角色权限配置（仅PI可操作）
    - 子管理员添加和权限分配
    - 权限范围：按单位、课题组划分

#### 2.2.2 工单调度中心
**功能描述**：工单分配和调度管理
**具体功能**：
- 工单管理
    - 新工单实时提醒
    - 手动/自动派单
    - 紧急工单标记和优先处理
    - 工单重新分配
- 调度跟踪
    - 工程师接单状态监控
    - 2小时内未响应自动提醒
    - 客户预约时间协调
    - 工程师实时位置跟踪（需要时）

#### 2.2.3 数据看板
**功能描述**：数据统计和分析
**具体功能**：
- 实时统计
    - 今日工单量
    - 工单完成率
    - 平均响应时间
    - 平均维修时长
- 多维分析
    - 故障类型分布统计
    - 工程师工作量统计
    - 客户满意度统计
    - 设备故障频率排行
- 报表导出
    - 支持Excel、PDF格式
    - 自定义时间范围
    - 自定义统计维度

#### 2.2.4 交易管理
**功能描述**：维修订单和费用管理
**具体功能**：
- 维修单管理
    - 多条件查询（单位、课题组、报修人、报修单号、时间范围）
    - 维修费用录入和审核
    - 发票信息管理
- 订单流程管理
    - 订单状态跟踪：客户提交→待确认→待维修→维修中→待验收→待结算→待开票→结算中→确认打款→完成
    - 各节点时间记录
    - 异常订单处理

#### 2.2.5 系统配置
**功能描述**：系统基础配置管理
**具体功能**：
- 设备数据库管理
    - 设备型号库维护
    - 常见故障库维护
    - 标准维修时长设置
- 系统通知管理
    - 公告发布（如节假日服务调整）
    - 通知模板配置
    - 推送设置管理
- 子管理员管理
    - 添加子管理员
    - 分配管理范围（按单位或课题组）
    - 权限范围限制

#### 2.2.6 库存管理
**功能描述**：配件库存管理
**具体功能**：
- 库存统计
- 配件入库/出库记录
- 库存预警设置
- 配件供应商管理

### 2.3 工程师功能模块

#### 2.3.1 工单处理
**功能描述**：接收和处理维修工单
**具体功能**：
- 工单接收
    - 新工单推送通知
    - 工单信息查看（设备信息、故障描述、客户联系方式）
    - 接单/拒单操作
- 状态更新
    - 状态选项：已接单→出发中→已到达→维修中→待配件→待验收→已完成
    - 实时位置更新（出发中、已到达状态）
    - 预计完成时间填写
- 维修记录
    - 故障原因分析（必填）
    - 维修过程记录（必填）
    - 更换配件记录（选填）
    - 现场照片上传（选填）
    - 维修报告生成

#### 2.3.2 备件管理
**功能描述**：配件申请和使用管理
**具体功能**：
- 配件申请
    - 配件需求提交
    - 申请状态跟踪
    - 到货通知接收
- 配件使用
    - 配件领用记录
    - 使用配件登记
    - 剩余配件归还

#### 2.3.3 客户服务
**功能描述**：与客户沟通和服务确认
**具体功能**：
- 服务沟通
    - 联系客户确认上门时间
    - 维修过程中的沟通记录
    - 特殊情况说明
- 服务确认
    - 客户签字确认（电子签名）
    - 维修结果确认
    - 服务满意度收集

## 3. 非功能需求

### 3.1 性能要求
- 系统并发用户数：支持1000+在线用户
- 响应时间：页面加载时间不超过3秒
- 数据库查询响应时间不超过1秒

### 3.2 安全要求
- 用户数据加密存储
- HTTPS协议传输
- 实名制注册和身份验证
- 数据访问权限控制
- 操作日志记录

### 3.3 兼容性要求
- 微信小程序支持

### 3.4 可用性要求
- 系统可用性不低于99.5%
- 7×24小时运行
- 数据定期备份

## 4. 技术架构建议
- 前端：微信小程序
- 后端：微信云
- 数据库：MySQL + Redis
- 消息推送：WebSocket + 第三方推送服务
- 文件存储：阿里云OSS或腾讯云COS

## 5. 项目交付物
- 需求规格说明书
- 系统设计文档
- 数据库设计文档
- API接口文档
- 用户操作手册
- 部署运维手册
- 源代码
- 测试报告

---

## 不确定点说明（需客户确认）

1. **用户注册流程**：是否需要管理员审核后才能使用系统？
2. **收费模式**：维修服务如何收费？是否需要在线支付功能？
3. **工程师管理**：工程师是平台自有还是第三方合作？如何管理工程师资质？
4. **设备二维码权限**：PI和管理员查看权限的具体范围需要明确
5. **数据保存期限**：聊天记录、维修记录等数据需要保存多长时间？
6. **通知方式**：除了系统内通知，是否需要短信、邮件等方式？
7. **移动端需求**：是否需要开发独立的APP，还是响应式网页即可？
8. **系统集成**：是否需要与现有的资产管理系统、财务系统等集成？
9. **多租户支持**：是否需要支持多个机构独立使用？
10. **备件供应商**：配件采购流程是否需要在系统中管理？