# 聊天界面和二维码功能测试指南

## 测试目标
验证聊天界面的新布局和设备二维码生成功能是否正常工作。

## 1. 聊天界面布局测试

### 测试步骤
1. 进入任意工单的聊天详情页面
2. 发送几条测试消息
3. 验证消息布局是否符合群聊样式

### 预期结果

#### 左侧消息（他人发送）
- ✅ 头像显示在左侧
- ✅ 昵称和时间显示在消息气泡上方
- ✅ 消息气泡为白色，左上角有小三角
- ✅ 整体布局左对齐

#### 右侧消息（自己发送）
- ✅ 头像显示在右侧
- ✅ 昵称和时间显示在消息气泡上方，右对齐
- ✅ 消息气泡为蓝色，右上角有小三角
- ✅ 整体布局右对齐

#### 样式细节
- ✅ 头像为圆形，带有白色边框和阴影
- ✅ 消息气泡有圆角和阴影效果
- ✅ 昵称和时间字体较小，颜色较淡
- ✅ 消息间距合适，易于阅读

## 2. 设备二维码功能测试

### 测试步骤

#### 2.1 生成二维码
1. 进入任意设备详情页面
2. 点击"生成二维码"按钮
3. 验证二维码是否正确生成

#### 2.2 保存二维码
1. 生成二维码后，点击"保存到相册"按钮
2. 授权相册权限（如需要）
3. 验证图片是否成功保存到相册

#### 2.3 分享二维码
1. 生成二维码后，点击"分享二维码"按钮
2. 验证是否弹出分享菜单
3. 测试分享到微信等平台

#### 2.4 扫描二维码
1. 使用另一台设备扫描生成的二维码
2. 验证扫描结果是否正确解析
3. 测试不同角色扫描的行为差异

### 预期结果

#### 二维码生成
- ✅ 点击按钮后显示加载提示
- ✅ 成功生成200x200像素的二维码
- ✅ 二维码包含设备信息的JSON数据
- ✅ 二维码中心有蓝色品牌标识

#### 二维码保存
- ✅ 成功保存到系统相册
- ✅ 权限不足时显示友好提示
- ✅ 保存成功后显示成功提示

#### 二维码分享
- ✅ 弹出系统分享菜单
- ✅ 可以分享到微信、QQ等平台
- ✅ 分享的图片清晰可用

#### 二维码扫描
- ✅ 普通用户扫描：跳转到设备详情页面
- ✅ 工程师扫描：显示权限提示，引导查看工单
- ✅ JSON格式正确解析
- ✅ 旧格式兼容性正常

## 3. 二维码数据格式验证

### JSON格式检查
验证生成的二维码包含以下JSON结构：
```json
{
  "type": "equipment",
  "version": "1.0",
  "timestamp": 1703123456789,
  "data": {
    "id": "设备ID",
    "name": "设备名称",
    "model": "设备型号",
    "serialNumber": "序列号",
    "location": "存放位置",
    "status": "设备状态",
    "qrCode": "EQ-设备ID-时间戳"
  },
  "actions": {
    "view": "/pages/equipment/detail/detail?id=设备ID",
    "repair": "/pages/orders/create/create?equipmentId=设备ID"
  }
}
```

### 数据完整性检查
- ✅ 所有必要字段都存在
- ✅ 设备ID正确
- ✅ 时间戳为当前时间
- ✅ 操作路径正确

## 4. 兼容性测试

### 旧格式二维码
1. 测试 `equipment:12345` 格式
2. 测试纯数字 `12345` 格式
3. 验证是否正确解析和跳转

### 错误处理
1. 测试无效JSON格式
2. 测试缺少必要字段的JSON
3. 验证错误提示是否友好

## 5. 权限测试

### 不同角色测试
1. **普通用户**：
   - 可以生成二维码
   - 扫描后跳转到设备详情
   
2. **PI用户**：
   - 可以生成二维码
   - 扫描后跳转到设备详情
   
3. **工程师**：
   - 可以生成二维码
   - 扫描后显示权限提示
   
4. **管理员**：
   - 可以生成二维码
   - 扫描后跳转到设备详情

## 6. 性能测试

### 生成速度
- ✅ 二维码生成时间 < 2秒
- ✅ Canvas绘制流畅无卡顿
- ✅ 保存图片速度正常

### 内存使用
- ✅ 生成多个二维码不会导致内存泄漏
- ✅ Canvas资源正确释放

## 7. 用户体验测试

### 界面友好性
- ✅ 按钮位置合理
- ✅ 操作流程直观
- ✅ 提示信息清晰
- ✅ 错误处理友好

### 功能可发现性
- ✅ 生成二维码按钮易于发现
- ✅ 保存和分享功能明显
- ✅ 扫描结果处理合理

## 故障排除

### 常见问题
1. **二维码无法生成**
   - 检查设备信息是否完整
   - 检查Canvas权限
   
2. **无法保存到相册**
   - 检查相册权限
   - 检查存储空间
   
3. **扫描无法识别**
   - 检查二维码清晰度
   - 检查JSON格式
   
4. **聊天布局错乱**
   - 检查CSS样式
   - 检查消息数据结构

### 调试方法
1. 查看控制台日志
2. 检查网络请求
3. 验证数据格式
4. 测试不同设备和系统版本
