const jwt = require('jsonwebtoken');
const { User, UserRole } = require('../models');

// JWT认证中间件
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 查找用户
    const user = await User.findByPk(decoded.userId);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: '用户已被禁用'
      });
    }

    // 添加角色上下文信息
    req.user = {
      ...user.toJSON(),
      currentRole: decoded.currentRole || user.role,
      roleId: decoded.roleId,
      researchGroupId: decoded.researchGroupId,
      permissions: decoded.permissions || []
    };
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '访问令牌已过期'
      });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的访问令牌'
      });
    }

    console.error('认证中间件错误:', error);
    res.status(500).json({
      success: false,
      message: '认证失败'
    });
  }
};

// 角色权限检查中间件
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证'
      });
    }

    const userRole = req.user.currentRole || req.user.role;
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    next();
  };
};

// 管理员权限检查
const requireAdmin = requireRole(['admin', 'super_admin']);

// 工程师权限检查
const requireEngineer = requireRole(['engineer', 'admin', 'super_admin']);

// 超级管理员权限检查
const requireSuperAdmin = requireRole(['super_admin']);

// 权限检查中间件
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证'
      });
    }

    const userPermissions = req.user.permissions || [];
    const userRole = req.user.currentRole || req.user.role;

    // 超级管理员拥有所有权限
    if (userRole === 'super_admin' || userPermissions.includes('*')) {
      return next();
    }

    // 检查是否有指定权限
    if (!userPermissions.includes(permission)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    next();
  };
};

// 检查是否为资源所有者或管理员
const requireOwnerOrAdmin = (getResourceOwnerId) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '未认证'
        });
      }

      const userRole = req.user.role;
      const userId = req.user.id;

      // 管理员可以访问所有资源
      if (['admin', 'super_admin'].includes(userRole)) {
        return next();
      }

      // 获取资源所有者ID
      const resourceOwnerId = await getResourceOwnerId(req);
      
      if (userId === resourceOwnerId) {
        return next();
      }

      return res.status(403).json({
        success: false,
        message: '只能访问自己的资源'
      });
    } catch (error) {
      console.error('权限检查错误:', error);
      res.status(500).json({
        success: false,
        message: '权限检查失败'
      });
    }
  };
};

// 微信小程序登录验证
const wechatAuth = async (req, res, next) => {
  try {
    const { code } = req.body;

    // 检查是否有云托管注入的用户信息
    const hasCloudUserInfo = req.headers['x-wx-openid'];

    // 如果没有云托管用户信息且没有code，则返回错误
    if (!hasCloudUserInfo && !code) {
      return res.status(400).json({
        success: false,
        message: '微信授权码缺失'
      });
    }

    // 如果有云托管用户信息，直接通过
    if (hasCloudUserInfo) {
      req.wechatData = {
        openid: req.headers['x-wx-openid'],
        session_key: req.headers['x-wx-session-key'],
        unionid: req.headers['x-wx-unionid']
      };
    } else {
      // 传统方式，使用code
      req.wechatData = {
        code: code
      };
    }

    next();
  } catch (error) {
    console.error('微信认证错误:', error);
    res.status(500).json({
      success: false,
      message: '微信认证失败'
    });
  }
};

module.exports = {
  authenticateToken,
  requireRole,
  requireAdmin,
  requireEngineer,
  requireSuperAdmin,
  requireOwnerOrAdmin,
  requirePermission,
  wechatAuth
};
