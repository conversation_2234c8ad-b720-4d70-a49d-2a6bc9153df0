const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { User, Chat } = require('../models');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId
  }

  init(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.NODE_ENV === 'production' 
          ? ['https://your-domain.com'] 
          : ['http://localhost:3000'],
        methods: ['GET', 'POST']
      }
    });

    // 认证中间件
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        if (!token) {
          return next(new Error('认证失败'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findByPk(decoded.userId);
        
        if (!user || !user.is_active) {
          return next(new Error('用户不存在或已禁用'));
        }

        socket.userId = user.id;
        socket.userRole = user.role;
        next();
      } catch (error) {
        next(new Error('认证失败'));
      }
    });

    this.io.on('connection', (socket) => {
      console.log(`用户 ${socket.userId} 连接到Socket.IO`);
      
      // 记录用户连接
      this.connectedUsers.set(socket.userId, socket.id);

      // 加入用户专属房间
      socket.join(`user_${socket.userId}`);

      // 处理加入工单房间
      socket.on('join_order', (orderId) => {
        socket.join(`order_${orderId}`);
        console.log(`用户 ${socket.userId} 加入工单 ${orderId} 房间`);
      });

      // 处理离开工单房间
      socket.on('leave_order', (orderId) => {
        socket.leave(`order_${orderId}`);
        console.log(`用户 ${socket.userId} 离开工单 ${orderId} 房间`);
      });

      // 处理发送消息
      socket.on('send_message', async (data) => {
        try {
          const { orderId, receiverId, messageType, content, fileUrl, fileName, fileSize, duration } = data;

          // 保存消息到数据库
          const message = await Chat.create({
            order_id: orderId,
            sender_id: socket.userId,
            receiver_id: receiverId,
            message_type: messageType,
            content,
            file_url: fileUrl,
            file_name: fileName,
            file_size: fileSize,
            duration
          });

          // 获取完整的消息信息
          const fullMessage = await Chat.findByPk(message.id, {
            include: [
              { model: User, as: 'sender', attributes: ['id', 'username', 'real_name', 'avatar'] },
              { model: User, as: 'receiver', attributes: ['id', 'username', 'real_name', 'avatar'] }
            ]
          });

          // 发送给工单房间的所有用户
          this.io.to(`order_${orderId}`).emit('new_message', fullMessage);

          // 发送给接收者（如果在线）
          if (this.connectedUsers.has(receiverId)) {
            this.io.to(`user_${receiverId}`).emit('message_notification', {
              orderId,
              senderId: socket.userId,
              messageType,
              content: messageType === 'text' ? content : `[${messageType}]`
            });
          }

        } catch (error) {
          console.error('发送消息错误:', error);
          socket.emit('error', { message: '发送消息失败' });
        }
      });

      // 处理消息已读
      socket.on('mark_read', async (data) => {
        try {
          const { messageIds } = data;
          
          await Chat.update(
            { is_read: true, read_at: new Date() },
            { 
              where: { 
                id: messageIds,
                receiver_id: socket.userId 
              } 
            }
          );

          socket.emit('messages_marked_read', { messageIds });
        } catch (error) {
          console.error('标记已读错误:', error);
        }
      });

      // 处理工单状态更新
      socket.on('order_status_update', (data) => {
        const { orderId, status, message } = data;
        
        // 广播给工单房间的所有用户
        this.io.to(`order_${orderId}`).emit('order_status_changed', {
          orderId,
          status,
          message,
          updatedBy: socket.userId,
          updatedAt: new Date()
        });
      });

      // 处理断开连接
      socket.on('disconnect', () => {
        console.log(`用户 ${socket.userId} 断开连接`);
        this.connectedUsers.delete(socket.userId);
      });
    });

    console.log('✅ Socket.IO服务初始化完成');
  }

  // 发送通知给特定用户
  sendNotificationToUser(userId, notification) {
    if (this.connectedUsers.has(userId)) {
      this.io.to(`user_${userId}`).emit('notification', notification);
    }
  }

  // 发送通知给工单相关用户
  sendNotificationToOrder(orderId, notification, excludeUserId = null) {
    this.io.to(`order_${orderId}`).emit('notification', notification);
  }

  // 广播系统通知
  broadcastSystemNotification(notification, roles = null) {
    if (roles) {
      // 发送给特定角色的用户
      this.connectedUsers.forEach((socketId, userId) => {
        const socket = this.io.sockets.sockets.get(socketId);
        if (socket && roles.includes(socket.userRole)) {
          socket.emit('system_notification', notification);
        }
      });
    } else {
      // 广播给所有用户
      this.io.emit('system_notification', notification);
    }
  }

  // 获取在线用户数量
  getOnlineUsersCount() {
    return this.connectedUsers.size;
  }

  // 检查用户是否在线
  isUserOnline(userId) {
    return this.connectedUsers.has(userId);
  }
}

module.exports = new SocketService();
