const { Sequelize } = require('sequelize');

// 解析 MYSQL_ADDRESS
const mysqlAddress = process.env.MYSQL_ADDRESS || '************:3306';
const [mysqlHost, mysqlPort] = mysqlAddress.split(':');

// MySQL数据库连接
const sequelize = new Sequelize(
  process.env.DB_NAME || 'ziguan_wei',
  process.env.MYSQL_USERNAME || process.env.DB_USER || 'root',
  process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD || '',
  {
    host: mysqlHost || process.env.DB_HOST || '************',
    port: parseInt(mysqlPort) || process.env.DB_PORT || 3306,
    dialect: 'mysql',
    timezone: '+08:00',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    define: {
      timestamps: true,
      underscored: true,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    }
  }
);

module.exports = {
  sequelize
};
