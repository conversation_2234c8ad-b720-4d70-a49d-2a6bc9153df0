const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  openid: {
    type: DataTypes.STRING(100),
    unique: true,
    allowNull: false,
    comment: '微信openid'
  },
  unionid: {
    type: DataTypes.STRING(100),
    unique: true,
    allowNull: true,
    comment: '微信unionid'
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '用户名'
  },
  real_name: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '真实姓名'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '手机号'
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '邮箱'
  },
  avatar: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '头像URL'
  },
  role: {
    type: DataTypes.ENUM('user', 'engineer', 'admin', 'super_admin'),
    defaultValue: 'user',
    comment: '用户角色：user-C端用户，engineer-工程师，admin-管理员，super_admin-超级管理员'
  },
  department: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '部门/课题组'
  },
  employee_id: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '工号'
  },
  id_card: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '身份证号'
  },
  is_verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否实名认证'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否启用'
  },
  last_login_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后登录时间'
  },
  permissions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '权限配置'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '创建人ID'
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '更新人ID'
  }
}, {
  tableName: 'users',
  comment: '用户表',
  indexes: [
    {
      fields: ['openid']
    },
    {
      fields: ['phone']
    },
    {
      fields: ['role']
    },
    {
      fields: ['department']
    }
  ]
});

// 实例方法
User.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  delete values.password;
  return values;
};

// 类方法
User.findByOpenid = function(openid) {
  return this.findOne({ where: { openid } });
};

User.findByPhone = function(phone) {
  return this.findOne({ where: { phone } });
};

module.exports = User;
