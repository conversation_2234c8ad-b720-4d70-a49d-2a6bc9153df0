const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Equipment = sequelize.define('Equipment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  equipment_code: {
    type: DataTypes.STRING(50),
    unique: true,
    allowNull: false,
    comment: '设备编号（唯一）'
  },
  maintenance_code: {
    type: DataTypes.STRING(50),
    unique: true,
    allowNull: false,
    comment: '维护编号（唯一）'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '设备名称'
  },
  brand: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '品牌'
  },
  model: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '型号'
  },
  serial_number: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '序列号'
  },
  category: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '设备类别'
  },
  location: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '设备位置'
  },
  purchase_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '采购日期'
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '启用日期'
  },
  warranty_end_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '保修截止日期'
  },
  is_under_warranty: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否在保修期内'
  },
  purchase_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '采购价格'
  },
  supplier: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '供应商'
  },
  supplier_contact: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '供应商联系方式'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '设备图片URLs'
  },
  specifications: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '设备规格说明'
  },
  manual_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '使用手册URL'
  },
  qr_code: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '设备二维码URL'
  },
  status: {
    type: DataTypes.ENUM('normal', 'maintenance', 'repair', 'scrapped'),
    defaultValue: 'normal',
    comment: '设备状态：normal-正常，maintenance-维护中，repair-维修中，scrapped-报废'
  },
  owner_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '设备负责人ID'
  },
  department: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '所属部门/课题组'
  },
  pi_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'PI（课题组负责人）ID'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注信息'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '创建人ID'
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '更新人ID'
  }
}, {
  tableName: 'equipment',
  comment: '设备表',
  indexes: [
    {
      fields: ['equipment_code']
    },
    {
      fields: ['maintenance_code']
    },
    {
      fields: ['owner_id']
    },
    {
      fields: ['department']
    },
    {
      fields: ['status']
    },
    {
      fields: ['brand', 'model']
    }
  ]
});

// 生成设备编号
Equipment.generateEquipmentCode = function() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `EQ${year}${month}${day}${random}`;
};

// 生成维护编号
Equipment.generateMaintenanceCode = function() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `MT${year}${month}${day}${random}`;
};

module.exports = Equipment;
