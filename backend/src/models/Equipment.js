const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Equipment = sequelize.define('Equipment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '设备名称'
  },
  maintenance_code: {
    type: DataTypes.STRING(100),
    unique: true,
    allowNull: false,
    comment: '维护编号（唯一）'
  },
  serial_number: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '序列号'
  },
  model_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '型号ID'
  },
  location: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '设备位置'
  },
  status: {
    type: DataTypes.ENUM('normal', 'maintenance', 'fault', 'retired'),
    defaultValue: 'normal',
    comment: '设备状态'
  },
  purchase_date: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    comment: '购买日期'
  },
  warranty_expiry: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    comment: '保修到期日'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '设备描述'
  },
  qr_code_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '二维码URL'
  },
  owner_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '负责人ID'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '创建人ID'
  }
}, {
  tableName: 'equipment',
  comment: '设备表',
  indexes: [
    {
      fields: ['maintenance_code']
    },
    {
      fields: ['serial_number']
    },
    {
      fields: ['owner_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['model_id']
    }
  ]
});

// 生成维护编号
Equipment.generateMaintenanceCode = function() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `EQ${year}${month}${day}${random}`;
};

module.exports = Equipment;
