const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Chat = sequelize.define('Chat', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  order_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '工单ID'
  },
  sender_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '发送者ID'
  },
  receiver_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '接收者ID'
  },
  message_type: {
    type: DataTypes.ENUM('text', 'image', 'voice', 'file', 'system'),
    defaultValue: 'text',
    comment: '消息类型：text-文本，image-图片，voice-语音，file-文件，system-系统消息'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '消息内容'
  },
  file_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '文件URL（图片、语音、文件）'
  },
  file_name: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '文件名'
  },
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '文件大小（字节）'
  },
  duration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '语音时长（秒）'
  },
  is_read: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否已读'
  },
  read_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '阅读时间'
  },
  is_deleted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否删除'
  },
  reply_to_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '回复的消息ID'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '消息元数据'
  }
}, {
  tableName: 'chats',
  comment: '聊天记录表',
  indexes: [
    {
      fields: ['order_id']
    },
    {
      fields: ['sender_id']
    },
    {
      fields: ['receiver_id']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['is_read']
    }
  ]
});

module.exports = Chat;
