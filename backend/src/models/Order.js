const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  order_number: {
    type: DataTypes.STRING(50),
    unique: true,
    allowNull: false,
    comment: '工单号'
  },
  equipment_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '设备ID'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '报修用户ID'
  },
  engineer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '分配的工程师ID'
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '工单标题'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '故障描述'
  },
  fault_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '故障类型'
  },
  priority: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
    defaultValue: 'normal',
    comment: '优先级：low-低，normal-普通，high-高，urgent-紧急'
  },
  status: {
    type: DataTypes.ENUM(
      'pending',      // 待处理
      'confirmed',    // 已确认
      'assigned',     // 已分配
      'in_progress',  // 维修中
      'waiting_parts',// 待配件
      'testing',      // 测试中
      'completed',    // 已完成
      'cancelled',    // 已取消
      'closed'        // 已关闭
    ),
    defaultValue: 'pending',
    comment: '工单状态'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '故障图片URLs'
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '联系电话'
  },
  contact_email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '联系邮箱'
  },
  expected_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '期望完成时间'
  },
  assigned_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '分配时间'
  },
  started_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '开始维修时间'
  },
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '完成时间'
  },
  response_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '响应时间（分钟）'
  },
  resolution_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '解决时间（分钟）'
  },
  cost_estimate: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '预估费用'
  },
  actual_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '实际费用'
  },
  parts_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '配件费用'
  },
  labor_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '人工费用'
  },
  is_warranty: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否保修'
  },
  warranty_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '保修类型'
  },
  solution: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '解决方案'
  },
  fault_cause: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '故障原因'
  },
  replaced_parts: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '更换的配件'
  },
  work_summary: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '工作总结'
  },
  customer_signature: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '客户签名图片URL'
  },
  engineer_signature: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '工程师签名图片URL'
  },
  rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '客户评分（1-5）'
  },
  feedback: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '客户反馈'
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '内部备注'
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '标签'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '创建人ID'
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '更新人ID'
  }
}, {
  tableName: 'orders',
  comment: '工单表',
  indexes: [
    {
      fields: ['order_number']
    },
    {
      fields: ['equipment_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['engineer_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['created_at']
    }
  ]
});

// 生成工单号
Order.generateOrderNumber = function() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `WO${year}${month}${day}${hour}${minute}${random}`;
};

module.exports = Order;
