const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const OrderLog = sequelize.define('OrderLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  order_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '工单ID'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '操作人ID'
  },
  action: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '操作类型'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '操作描述'
  },
  old_status: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '原状态'
  },
  new_status: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '新状态'
  },
  old_data: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '原数据'
  },
  new_data: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '新数据'
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
    comment: 'IP地址'
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户代理'
  }
}, {
  tableName: 'order_logs',
  comment: '工单操作日志表',
  indexes: [
    {
      fields: ['order_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['action']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = OrderLog;
