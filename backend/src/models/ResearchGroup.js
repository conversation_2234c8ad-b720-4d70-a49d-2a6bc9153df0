const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ResearchGroup = sequelize.define('ResearchGroup', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '课题组名称'
  },
  organization_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '所属单位ID'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '课题组描述'
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '联系电话'
  },
  contact_email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '联系邮箱'
  },
  address: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '地址'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否激活'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '创建人ID'
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '更新人ID'
  }
}, {
  tableName: 'research_groups',
  comment: '课题组表',
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['organization_id']
    },
    {
      fields: ['is_active']
    }
  ]
});

// 实例方法
ResearchGroup.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return values;
};

// 类方法
ResearchGroup.findByName = function(name) {
  return this.findOne({ 
    where: { 
      name: name,
      is_active: true
    }
  });
};

ResearchGroup.findActiveGroups = function(options = {}) {
  const { page = 1, limit = 10, keyword, organizationId } = options;
  const offset = (page - 1) * limit;
  const where = { is_active: true };

  if (keyword) {
    where.name = { [sequelize.Sequelize.Op.like]: `%${keyword}%` };
  }

  if (organizationId) {
    where.organization_id = organizationId;
  }

  return this.findAndCountAll({
    where,
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['created_at', 'DESC']]
  });
};

// 获取课题组的PI列表
ResearchGroup.prototype.getPIs = function() {
  const UserRole = require('./UserRole');
  const User = require('./User');
  
  return UserRole.findAll({
    where: {
      research_group_id: this.id,
      role: 'pi',
      is_active: true
    },
    include: [{
      model: User,
      as: 'user',
      attributes: ['id', 'username', 'real_name', 'phone', 'email', 'avatar']
    }]
  });
};

// 获取课题组的成员列表
ResearchGroup.prototype.getMembers = function() {
  const UserRole = require('./UserRole');
  const User = require('./User');
  
  return UserRole.findAll({
    where: {
      research_group_id: this.id,
      is_active: true
    },
    include: [{
      model: User,
      as: 'user',
      attributes: ['id', 'username', 'real_name', 'phone', 'email', 'avatar']
    }],
    order: [['role', 'ASC'], ['created_at', 'ASC']]
  });
};

// 获取课题组的设备数量
ResearchGroup.prototype.getEquipmentCount = async function() {
  const Equipment = require('./Equipment');
  
  return await Equipment.count({
    where: {
      research_group_id: this.id,
      deleted_at: null
    }
  });
};

// 获取课题组的工单数量
ResearchGroup.prototype.getOrderCount = async function() {
  const Order = require('./Order');
  const Equipment = require('./Equipment');
  
  return await Order.count({
    include: [{
      model: Equipment,
      as: 'equipment',
      where: {
        research_group_id: this.id
      }
    }]
  });
};

// 检查用户是否属于该课题组
ResearchGroup.prototype.hasMember = async function(userId) {
  const UserRole = require('./UserRole');
  
  const userRole = await UserRole.findOne({
    where: {
      user_id: userId,
      research_group_id: this.id,
      is_active: true
    }
  });
  
  return !!userRole;
};

// 检查用户是否为该课题组的PI
ResearchGroup.prototype.hasPI = async function(userId) {
  const UserRole = require('./UserRole');
  
  const userRole = await UserRole.findOne({
    where: {
      user_id: userId,
      research_group_id: this.id,
      role: 'pi',
      is_active: true
    }
  });
  
  return !!userRole;
};

module.exports = ResearchGroup;
