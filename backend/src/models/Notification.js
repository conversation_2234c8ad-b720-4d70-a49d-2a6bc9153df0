const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Notification = sequelize.define('Notification', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '接收用户ID'
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '通知标题'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '通知内容'
  },
  type: {
    type: DataTypes.ENUM(
      'order_created',      // 工单创建
      'order_assigned',     // 工单分配
      'order_status_changed', // 工单状态变更
      'order_completed',    // 工单完成
      'message_received',   // 收到消息
      'system_announcement', // 系统公告
      'maintenance_reminder', // 维护提醒
      'payment_reminder',   // 付款提醒
      'other'              // 其他
    ),
    allowNull: false,
    comment: '通知类型'
  },
  related_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联ID（如工单ID）'
  },
  related_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '关联类型（如order、equipment）'
  },
  is_read: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否已读'
  },
  read_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '阅读时间'
  },
  is_pushed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否已推送'
  },
  pushed_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '推送时间'
  },
  push_result: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '推送结果'
  },
  priority: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
    defaultValue: 'normal',
    comment: '优先级'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '过期时间'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '元数据'
  }
}, {
  tableName: 'notifications',
  comment: '通知表',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['is_read']
    },
    {
      fields: ['is_pushed']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['related_id', 'related_type']
    }
  ]
});

module.exports = Notification;
