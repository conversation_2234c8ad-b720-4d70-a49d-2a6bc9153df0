const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const PartUsage = sequelize.define('PartUsage', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  order_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '工单ID'
  },
  part_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '配件ID'
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '使用数量'
  },
  unit_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '单价'
  },
  total_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '总价'
  },
  usage_type: {
    type: DataTypes.ENUM('replacement', 'repair', 'upgrade', 'maintenance'),
    defaultValue: 'replacement',
    comment: '使用类型：replacement-更换，repair-维修，upgrade-升级，maintenance-维护'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '使用说明'
  },
  used_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '使用人ID（工程师）'
  },
  approved_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '审批人ID'
  },
  approved_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '审批时间'
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'used'),
    defaultValue: 'pending',
    comment: '状态：pending-待审批，approved-已审批，rejected-已拒绝，used-已使用'
  }
}, {
  tableName: 'part_usages',
  comment: '配件使用记录表',
  indexes: [
    {
      fields: ['order_id']
    },
    {
      fields: ['part_id']
    },
    {
      fields: ['used_by']
    },
    {
      fields: ['status']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = PartUsage;
