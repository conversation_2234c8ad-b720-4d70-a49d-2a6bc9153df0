const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserRole = sequelize.define('UserRole', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  role: {
    type: DataTypes.ENUM('user', 'pi', 'engineer', 'admin', 'super_admin'),
    allowNull: false,
    comment: '角色类型'
  },
  role_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '角色显示名称'
  },
  department: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '角色所属部门/课题组'
  },
  research_group_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联的课题组ID（适用于PI和用户角色）'
  },
  permissions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '角色权限配置'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '角色是否激活'
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否为默认角色'
  },
  granted_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '授权人ID'
  },
  granted_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '授权时间'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '角色过期时间（可选）'
  }
}, {
  tableName: 'user_roles',
  comment: '用户角色关联表',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['role']
    },
    {
      fields: ['user_id', 'role'],
      unique: true
    },
    {
      fields: ['research_group_id']
    }
  ]
});

// 实例方法
UserRole.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return values;
};

// 类方法
UserRole.findByUserId = function(userId) {
  return this.findAll({ 
    where: { 
      user_id: userId,
      is_active: true
    },
    order: [['is_default', 'DESC'], ['created_at', 'ASC']]
  });
};

UserRole.findUserRole = function(userId, role) {
  return this.findOne({ 
    where: { 
      user_id: userId,
      role: role,
      is_active: true
    }
  });
};

UserRole.getUserDefaultRole = function(userId) {
  return this.findOne({ 
    where: { 
      user_id: userId,
      is_default: true,
      is_active: true
    }
  });
};

// 获取用户在指定课题组的角色
UserRole.getUserRoleInGroup = function(userId, researchGroupId) {
  return this.findAll({ 
    where: { 
      user_id: userId,
      research_group_id: researchGroupId,
      is_active: true
    }
  });
};

// 获取课题组的所有PI
UserRole.getGroupPIs = function(researchGroupId) {
  return this.findAll({ 
    where: { 
      research_group_id: researchGroupId,
      role: 'pi',
      is_active: true
    },
    include: [{
      model: require('./User'),
      as: 'user',
      attributes: ['id', 'username', 'real_name', 'phone', 'email']
    }]
  });
};

// 检查用户是否有指定角色
UserRole.hasRole = async function(userId, role) {
  const userRole = await this.findOne({
    where: {
      user_id: userId,
      role: role,
      is_active: true
    }
  });
  return !!userRole;
};

// 检查用户是否为课题组PI
UserRole.isGroupPI = async function(userId, researchGroupId) {
  const userRole = await this.findOne({
    where: {
      user_id: userId,
      research_group_id: researchGroupId,
      role: 'pi',
      is_active: true
    }
  });
  return !!userRole;
};

// 获取用户管理的课题组
UserRole.getManagedGroups = function(userId) {
  return this.findAll({
    where: {
      user_id: userId,
      role: 'pi',
      is_active: true
    },
    attributes: ['research_group_id'],
    group: ['research_group_id']
  });
};

module.exports = UserRole;
