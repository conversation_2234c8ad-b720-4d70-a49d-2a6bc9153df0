const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Part = sequelize.define('Part', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  part_code: {
    type: DataTypes.STRING(50),
    unique: true,
    allowNull: false,
    comment: '配件编号'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '配件名称'
  },
  category: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '配件类别'
  },
  brand: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '品牌'
  },
  model: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '型号'
  },
  specification: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '规格说明'
  },
  unit: {
    type: DataTypes.STRING(20),
    defaultValue: '个',
    comment: '计量单位'
  },
  unit_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '单价'
  },
  stock_quantity: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '库存数量'
  },
  min_stock: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '最低库存'
  },
  max_stock: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '最高库存'
  },
  location: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '存放位置'
  },
  supplier: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '供应商'
  },
  supplier_contact: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '供应商联系方式'
  },
  lead_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '采购周期（天）'
  },
  warranty_period: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '保修期（月）'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '配件图片URLs'
  },
  datasheet_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '技术资料URL'
  },
  compatible_equipment: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '兼容设备列表'
  },
  status: {
    type: DataTypes.ENUM('active', 'discontinued', 'out_of_stock'),
    defaultValue: 'active',
    comment: '状态：active-正常，discontinued-停产，out_of_stock-缺货'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '创建人ID'
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '更新人ID'
  }
}, {
  tableName: 'parts',
  comment: '配件表',
  indexes: [
    {
      fields: ['part_code']
    },
    {
      fields: ['category']
    },
    {
      fields: ['brand', 'model']
    },
    {
      fields: ['status']
    },
    {
      fields: ['stock_quantity']
    }
  ]
});

// 生成配件编号
Part.generatePartCode = function() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `PT${year}${month}${day}${random}`;
};

module.exports = Part;
