const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Organization = sequelize.define('Organization', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    comment: '单位名称'
  },
  short_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '单位简称'
  },
  type: {
    type: DataTypes.ENUM('university', 'institute', 'company', 'government', 'other'),
    defaultValue: 'university',
    comment: '单位类型'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '单位描述'
  },
  contact_person: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '联系人'
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '联系电话'
  },
  contact_email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '联系邮箱'
  },
  address: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '地址'
  },
  website: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '官网地址'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否激活'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '创建人ID'
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '更新人ID'
  }
}, {
  tableName: 'organizations',
  comment: '组织/单位表',
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['type']
    },
    {
      fields: ['is_active']
    }
  ]
});

// 实例方法
Organization.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return values;
};

// 类方法
Organization.findByName = function(name) {
  return this.findOne({ 
    where: { 
      name: name,
      is_active: true
    }
  });
};

Organization.findActiveOrganizations = function(options = {}) {
  const { page = 1, limit = 10, keyword, type } = options;
  const offset = (page - 1) * limit;
  const where = { is_active: true };

  if (keyword) {
    where[sequelize.Sequelize.Op.or] = [
      { name: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } },
      { short_name: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } }
    ];
  }

  if (type) {
    where.type = type;
  }

  return this.findAndCountAll({
    where,
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['created_at', 'DESC']]
  });
};

// 获取组织下的课题组数量
Organization.prototype.getResearchGroupCount = async function() {
  const ResearchGroup = require('./ResearchGroup');
  
  return await ResearchGroup.count({
    where: {
      organization_id: this.id,
      is_active: true
    }
  });
};

// 获取组织下的课题组列表
Organization.prototype.getResearchGroups = function(options = {}) {
  const ResearchGroup = require('./ResearchGroup');
  const { page = 1, limit = 10, keyword } = options;
  const offset = (page - 1) * limit;
  const where = { 
    organization_id: this.id,
    is_active: true
  };

  if (keyword) {
    where.name = { [sequelize.Sequelize.Op.like]: `%${keyword}%` };
  }

  return ResearchGroup.findAndCountAll({
    where,
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['created_at', 'DESC']]
  });
};

// 获取组织的统计信息
Organization.prototype.getStatistics = async function() {
  const ResearchGroup = require('./ResearchGroup');
  const Equipment = require('./Equipment');
  const Order = require('./Order');

  const [groupCount, equipmentCount, orderCount] = await Promise.all([
    // 课题组数量
    ResearchGroup.count({
      where: {
        organization_id: this.id,
        is_active: true
      }
    }),
    // 设备数量
    Equipment.count({
      include: [{
        model: ResearchGroup,
        as: 'researchGroup',
        where: {
          organization_id: this.id,
          is_active: true
        }
      }],
      where: {
        deleted_at: null
      }
    }),
    // 工单数量
    Order.count({
      include: [{
        model: Equipment,
        as: 'equipment',
        include: [{
          model: ResearchGroup,
          as: 'researchGroup',
          where: {
            organization_id: this.id,
            is_active: true
          }
        }]
      }]
    })
  ]);

  return {
    groupCount,
    equipmentCount,
    orderCount
  };
};

module.exports = Organization;
