const express = require('express');
const app = express();
const PORT = process.env.PORT || 80;

// 基本中间件
app.use(express.json());

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    port: PORT
  });
});

// 根路径
app.get('/', (req, res) => {
  res.json({ 
    message: '资管维平台 API 服务',
    status: 'running',
    version: '1.0.0'
  });
});

// 测试API
app.get('/api/test', (req, res) => {
  res.json({ 
    success: true,
    message: 'API 测试成功',
    timestamp: new Date().toISOString()
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  });
});

// 启动服务器
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 简化版服务器运行在 0.0.0.0:${PORT}`);
  console.log(`📋 环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 健康检查: http://0.0.0.0:${PORT}/health`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

module.exports = app;
