const { User, Order, Equipment, Part, PartUsage, Notification } = require('../models');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

class AdminController {
  // 获取数据看板
  async getDashboard(req, res) {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const [
        totalUsers,
        totalEquipment,
        totalOrders,
        todayOrders,
        unassignedOrders,
        inProgressOrders,
        completedOrders,
        lowStockParts,
        completionRate
      ] = await Promise.all([
        // 总用户数
        User.count(),
        // 总设备数
        Equipment.count(),
        // 总工单数
        Order.count(),
        // 今日工单数
        Order.count({
          where: {
            created_at: { [Op.gte]: today }
          }
        }),
        // 未分配工单数
        Order.count({
          where: {
            status: 'pending'
          }
        }),
        // 进行中工单数
        Order.count({
          where: {
            status: ['assigned', 'in_progress', 'waiting_parts', 'testing']
          }
        }),
        // 已完成工单数
        Order.count({
          where: {
            status: 'completed'
          }
        }),
        // 低库存配件数
        Part.count({
          where: {
            stock_quantity: {
              [Op.lte]: sequelize.col('min_stock')
            }
          }
        }),
        // 本月完成率
        this.calculateCompletionRate(thisMonth)
      ]);

      // 获取最近7天的工单趋势
      const orderTrend = await this.getOrderTrend(7);

      // 获取设备状态分布
      const equipmentStatus = await this.getEquipmentStatusDistribution();

      // 获取工程师工作负载
      const engineerWorkload = await this.getEngineerWorkloadSummary();

      res.json({
        success: true,
        data: {
          totalUsers,
          totalEquipment,
          totalOrders,
          todayOrders,
          unassignedOrders,
          inProgressOrders,
          completedOrders,
          lowStockParts,
          completionRate,
          orderTrend,
          equipmentStatus,
          engineerWorkload
        }
      });
    } catch (error) {
      console.error('获取数据看板错误:', error);
      res.status(500).json({
        success: false,
        message: '获取数据看板失败'
      });
    }
  }

  // 计算完成率
  async calculateCompletionRate(startDate) {
    try {
      const [totalOrders, completedOrders] = await Promise.all([
        Order.count({
          where: {
            created_at: { [Op.gte]: startDate }
          }
        }),
        Order.count({
          where: {
            created_at: { [Op.gte]: startDate },
            status: 'completed'
          }
        })
      ]);

      return totalOrders > 0 ? Math.round((completedOrders / totalOrders) * 100) : 0;
    } catch (error) {
      console.error('计算完成率错误:', error);
      return 0;
    }
  }

  // 获取工单趋势
  async getOrderTrend(days) {
    try {
      const trend = [];
      const now = new Date();

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const endOfDay = new Date(startOfDay);
        endOfDay.setDate(endOfDay.getDate() + 1);

        const count = await Order.count({
          where: {
            created_at: {
              [Op.gte]: startOfDay,
              [Op.lt]: endOfDay
            }
          }
        });

        trend.push({
          date: startOfDay.toISOString().split('T')[0],
          count
        });
      }

      return trend;
    } catch (error) {
      console.error('获取工单趋势错误:', error);
      return [];
    }
  }

  // 获取设备状态分布
  async getEquipmentStatusDistribution() {
    try {
      const distribution = await Equipment.findAll({
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status']
      });

      return distribution.map(item => ({
        status: item.status,
        count: parseInt(item.dataValues.count)
      }));
    } catch (error) {
      console.error('获取设备状态分布错误:', error);
      return [];
    }
  }

  // 获取工程师工作负载摘要
  async getEngineerWorkloadSummary() {
    try {
      const engineers = await User.findAll({
        where: {
          role: 'engineer',
          is_active: true
        },
        attributes: ['id', 'real_name', 'username']
      });

      const workloadSummary = await Promise.all(
        engineers.map(async (engineer) => {
          const activeOrders = await Order.count({
            where: {
              engineer_id: engineer.id,
              status: ['assigned', 'in_progress', 'waiting_parts', 'testing']
            }
          });

          return {
            engineerId: engineer.id,
            engineerName: engineer.real_name || engineer.username,
            activeOrders,
            workload: this.calculateWorkload(activeOrders)
          };
        })
      );

      return workloadSummary;
    } catch (error) {
      console.error('获取工程师工作负载摘要错误:', error);
      return [];
    }
  }

  // 计算工作负载等级
  calculateWorkload(activeOrders) {
    if (activeOrders >= 10) return 'high';
    if (activeOrders >= 5) return 'medium';
    return 'low';
  }

  // 获取工单统计
  async getOrderStats(req, res) {
    try {
      const { timeRange = 'month' } = req.query;
      
      let startDate;
      const now = new Date();

      switch (timeRange) {
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      }

      // 状态统计
      const statusStats = await Order.findAll({
        where: {
          created_at: { [Op.gte]: startDate }
        },
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status']
      });

      // 优先级统计
      const priorityStats = await Order.findAll({
        where: {
          created_at: { [Op.gte]: startDate }
        },
        attributes: [
          'priority',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['priority']
      });

      // 响应时间统计
      const responseTimeStats = await Order.findOne({
        where: {
          created_at: { [Op.gte]: startDate },
          response_time: { [Op.not]: null }
        },
        attributes: [
          [sequelize.fn('AVG', sequelize.col('response_time')), 'avgResponseTime'],
          [sequelize.fn('MIN', sequelize.col('response_time')), 'minResponseTime'],
          [sequelize.fn('MAX', sequelize.col('response_time')), 'maxResponseTime']
        ]
      });

      res.json({
        success: true,
        data: {
          statusStats: statusStats.map(item => ({
            status: item.status,
            count: parseInt(item.dataValues.count)
          })),
          priorityStats: priorityStats.map(item => ({
            priority: item.priority,
            count: parseInt(item.dataValues.count)
          })),
          responseTimeStats: responseTimeStats?.dataValues || {}
        }
      });
    } catch (error) {
      console.error('获取工单统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取工单统计失败'
      });
    }
  }

  // 获取设备统计
  async getEquipmentStats(req, res) {
    try {
      // 设备状态统计
      const statusStats = await Equipment.findAll({
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status']
      });

      // 设备品牌统计
      const brandStats = await Equipment.findAll({
        attributes: [
          'brand',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['brand'],
        order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
        limit: 10
      });

      // 设备类别统计
      const categoryStats = await Equipment.findAll({
        attributes: [
          'category',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['category'],
        order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
        limit: 10
      });

      // 保修状态统计
      const warrantyStats = await Equipment.findAll({
        attributes: [
          'is_under_warranty',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['is_under_warranty']
      });

      res.json({
        success: true,
        data: {
          statusStats: statusStats.map(item => ({
            status: item.status,
            count: parseInt(item.dataValues.count)
          })),
          brandStats: brandStats.map(item => ({
            brand: item.brand,
            count: parseInt(item.dataValues.count)
          })),
          categoryStats: categoryStats.map(item => ({
            category: item.category,
            count: parseInt(item.dataValues.count)
          })),
          warrantyStats: warrantyStats.map(item => ({
            isUnderWarranty: item.is_under_warranty,
            count: parseInt(item.dataValues.count)
          }))
        }
      });
    } catch (error) {
      console.error('获取设备统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取设备统计失败'
      });
    }
  }

  // 获取用户统计
  async getUserStats(req, res) {
    try {
      // 角色统计
      const roleStats = await User.findAll({
        attributes: [
          'role',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['role']
      });

      // 部门统计
      const departmentStats = await User.findAll({
        where: {
          department: { [Op.not]: null }
        },
        attributes: [
          'department',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['department'],
        order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
        limit: 10
      });

      // 认证状态统计
      const verificationStats = await User.findAll({
        attributes: [
          'is_verified',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['is_verified']
      });

      // 活跃状态统计
      const activeStats = await User.findAll({
        attributes: [
          'is_active',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['is_active']
      });

      res.json({
        success: true,
        data: {
          roleStats: roleStats.map(item => ({
            role: item.role,
            count: parseInt(item.dataValues.count)
          })),
          departmentStats: departmentStats.map(item => ({
            department: item.department,
            count: parseInt(item.dataValues.count)
          })),
          verificationStats: verificationStats.map(item => ({
            isVerified: item.is_verified,
            count: parseInt(item.dataValues.count)
          })),
          activeStats: activeStats.map(item => ({
            isActive: item.is_active,
            count: parseInt(item.dataValues.count)
          }))
        }
      });
    } catch (error) {
      console.error('获取用户统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取用户统计失败'
      });
    }
  }

  // 获取工程师绩效统计
  async getEngineerStats(req, res) {
    try {
      const { timeRange = 'month' } = req.query;
      
      let startDate;
      const now = new Date();

      switch (timeRange) {
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      }

      const engineers = await User.findAll({
        where: {
          role: 'engineer',
          is_active: true
        },
        attributes: ['id', 'real_name', 'username']
      });

      const engineerStats = await Promise.all(
        engineers.map(async (engineer) => {
          const [
            completedOrders,
            avgRating,
            avgResponseTime,
            totalWorkTime
          ] = await Promise.all([
            Order.count({
              where: {
                engineer_id: engineer.id,
                status: 'completed',
                completed_at: { [Op.gte]: startDate }
              }
            }),
            Order.findOne({
              where: {
                engineer_id: engineer.id,
                rating: { [Op.not]: null },
                completed_at: { [Op.gte]: startDate }
              },
              attributes: [
                [sequelize.fn('AVG', sequelize.col('rating')), 'avgRating']
              ]
            }),
            Order.findOne({
              where: {
                engineer_id: engineer.id,
                response_time: { [Op.not]: null },
                completed_at: { [Op.gte]: startDate }
              },
              attributes: [
                [sequelize.fn('AVG', sequelize.col('response_time')), 'avgResponseTime']
              ]
            }),
            Order.findOne({
              where: {
                engineer_id: engineer.id,
                resolution_time: { [Op.not]: null },
                completed_at: { [Op.gte]: startDate }
              },
              attributes: [
                [sequelize.fn('SUM', sequelize.col('resolution_time')), 'totalWorkTime']
              ]
            })
          ]);

          return {
            engineerId: engineer.id,
            engineerName: engineer.real_name || engineer.username,
            completedOrders,
            avgRating: avgRating?.dataValues?.avgRating ? parseFloat(avgRating.dataValues.avgRating).toFixed(1) : 0,
            avgResponseTime: avgResponseTime?.dataValues?.avgResponseTime ? Math.round(avgResponseTime.dataValues.avgResponseTime) : 0,
            totalWorkTime: totalWorkTime?.dataValues?.totalWorkTime ? Math.round(totalWorkTime.dataValues.totalWorkTime / 60) : 0 // 转换为小时
          };
        })
      );

      res.json({
        success: true,
        data: engineerStats
      });
    } catch (error) {
      console.error('获取工程师绩效统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取工程师绩效统计失败'
      });
    }
  }

  // 获取配件列表
  async getParts(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        category,
        status,
        keyword,
        lowStock
      } = req.query;

      const offset = (page - 1) * limit;
      const where = {};

      if (category) {
        where.category = category;
      }

      if (status) {
        where.status = status;
      }

      if (keyword) {
        where[Op.or] = [
          { part_code: { [Op.like]: `%${keyword}%` } },
          { name: { [Op.like]: `%${keyword}%` } },
          { brand: { [Op.like]: `%${keyword}%` } },
          { model: { [Op.like]: `%${keyword}%` } }
        ];
      }

      if (lowStock === 'true') {
        where.stock_quantity = {
          [Op.lte]: sequelize.col('min_stock')
        };
      }

      const { count, rows } = await Part.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset,
        order: [['created_at', 'DESC']]
      });

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取配件列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取配件列表失败'
      });
    }
  }

  // 创建配件
  async createPart(req, res) {
    try {
      const {
        name,
        category,
        brand,
        model,
        specification,
        unit,
        unit_price,
        min_stock,
        max_stock,
        location,
        supplier,
        supplier_contact,
        lead_time,
        warranty_period,
        compatible_equipment
      } = req.body;

      // 生成配件编号
      const partCode = Part.generatePartCode();

      const part = await Part.create({
        part_code: partCode,
        name,
        category,
        brand,
        model,
        specification,
        unit,
        unit_price,
        min_stock,
        max_stock,
        location,
        supplier,
        supplier_contact,
        lead_time,
        warranty_period,
        compatible_equipment,
        created_by: req.user.id
      });

      res.status(201).json({
        success: true,
        message: '配件创建成功',
        data: part
      });
    } catch (error) {
      console.error('创建配件错误:', error);
      res.status(500).json({
        success: false,
        message: '创建配件失败'
      });
    }
  }

  // 更新配件
  async updatePart(req, res) {
    try {
      const { id } = req.params;
      const updateData = { ...req.body, updated_by: req.user.id };

      const part = await Part.findByPk(id);
      if (!part) {
        return res.status(404).json({
          success: false,
          message: '配件不存在'
        });
      }

      await part.update(updateData);

      res.json({
        success: true,
        message: '配件更新成功',
        data: part
      });
    } catch (error) {
      console.error('更新配件错误:', error);
      res.status(500).json({
        success: false,
        message: '更新配件失败'
      });
    }
  }

  // 删除配件
  async deletePart(req, res) {
    try {
      const { id } = req.params;

      const part = await Part.findByPk(id);
      if (!part) {
        return res.status(404).json({
          success: false,
          message: '配件不存在'
        });
      }

      // 检查是否有相关的使用记录
      const usageCount = await PartUsage.count({
        where: { part_id: id }
      });

      if (usageCount > 0) {
        return res.status(400).json({
          success: false,
          message: '该配件有使用记录，无法删除'
        });
      }

      await part.destroy();

      res.json({
        success: true,
        message: '配件删除成功'
      });
    } catch (error) {
      console.error('删除配件错误:', error);
      res.status(500).json({
        success: false,
        message: '删除配件失败'
      });
    }
  }

  // 配件入库
  async stockInPart(req, res) {
    try {
      const { id } = req.params;
      const { quantity, notes } = req.body;

      const part = await Part.findByPk(id);
      if (!part) {
        return res.status(404).json({
          success: false,
          message: '配件不存在'
        });
      }

      await part.update({
        stock_quantity: sequelize.literal(`stock_quantity + ${quantity}`),
        updated_by: req.user.id
      });

      // 这里可以记录入库日志

      res.json({
        success: true,
        message: '入库成功'
      });
    } catch (error) {
      console.error('配件入库错误:', error);
      res.status(500).json({
        success: false,
        message: '入库失败'
      });
    }
  }

  // 配件出库
  async stockOutPart(req, res) {
    try {
      const { id } = req.params;
      const { quantity, notes } = req.body;

      const part = await Part.findByPk(id);
      if (!part) {
        return res.status(404).json({
          success: false,
          message: '配件不存在'
        });
      }

      if (part.stock_quantity < quantity) {
        return res.status(400).json({
          success: false,
          message: '库存不足'
        });
      }

      await part.update({
        stock_quantity: sequelize.literal(`stock_quantity - ${quantity}`),
        updated_by: req.user.id
      });

      // 这里可以记录出库日志

      res.json({
        success: true,
        message: '出库成功'
      });
    } catch (error) {
      console.error('配件出库错误:', error);
      res.status(500).json({
        success: false,
        message: '出库失败'
      });
    }
  }

  // 获取配件库存预警
  async getPartAlerts(req, res) {
    try {
      const lowStockParts = await Part.findAll({
        where: {
          stock_quantity: {
            [Op.lte]: sequelize.col('min_stock')
          },
          status: 'active'
        },
        order: [['stock_quantity', 'ASC']]
      });

      res.json({
        success: true,
        data: lowStockParts
      });
    } catch (error) {
      console.error('获取配件库存预警错误:', error);
      res.status(500).json({
        success: false,
        message: '获取配件库存预警失败'
      });
    }
  }

  // 获取系统配置
  async getSystemConfig(req, res) {
    try {
      // 这里应该从配置表或配置文件中读取
      // 暂时返回模拟数据
      const config = {
        systemName: '资管维平台',
        version: '1.0.0',
        maintenanceMode: false,
        maxFileSize: 10485760, // 10MB
        allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        orderAutoAssign: false,
        notificationEnabled: true,
        emailNotification: true,
        smsNotification: false
      };

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      console.error('获取系统配置错误:', error);
      res.status(500).json({
        success: false,
        message: '获取系统配置失败'
      });
    }
  }

  // 更新系统配置
  async updateSystemConfig(req, res) {
    try {
      const config = req.body;

      // 这里应该更新配置表或配置文件
      // 暂时只返回成功响应

      res.json({
        success: true,
        message: '系统配置更新成功'
      });
    } catch (error) {
      console.error('更新系统配置错误:', error);
      res.status(500).json({
        success: false,
        message: '更新系统配置失败'
      });
    }
  }

  // 获取公告列表
  async getAnnouncements(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        status
      } = req.query;

      const offset = (page - 1) * limit;
      const where = {};

      if (status) {
        where.status = status;
      }

      // 这里应该从公告表中查询
      // 暂时返回模拟数据
      const announcements = [
        {
          id: 1,
          title: '系统维护通知',
          content: '系统将于本周六进行维护，预计维护时间2小时',
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        }
      ];

      res.json({
        success: true,
        data: {
          list: announcements,
          total: announcements.length,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(announcements.length / limit)
        }
      });
    } catch (error) {
      console.error('获取公告列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取公告列表失败'
      });
    }
  }

  // 创建公告
  async createAnnouncement(req, res) {
    try {
      const { title, content, status = 'active' } = req.body;

      // 这里应该创建公告记录
      // 暂时只返回成功响应

      res.status(201).json({
        success: true,
        message: '公告创建成功'
      });
    } catch (error) {
      console.error('创建公告错误:', error);
      res.status(500).json({
        success: false,
        message: '创建公告失败'
      });
    }
  }

  // 更新公告
  async updateAnnouncement(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // 这里应该更新公告记录
      // 暂时只返回成功响应

      res.json({
        success: true,
        message: '公告更新成功'
      });
    } catch (error) {
      console.error('更新公告错误:', error);
      res.status(500).json({
        success: false,
        message: '更新公告失败'
      });
    }
  }

  // 删除公告
  async deleteAnnouncement(req, res) {
    try {
      const { id } = req.params;

      // 这里应该删除公告记录
      // 暂时只返回成功响应

      res.json({
        success: true,
        message: '公告删除成功'
      });
    } catch (error) {
      console.error('删除公告错误:', error);
      res.status(500).json({
        success: false,
        message: '删除公告失败'
      });
    }
  }
}

module.exports = new AdminController();
