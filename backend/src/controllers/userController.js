const { User } = require('../models');
const { Op } = require('sequelize');

class UserController {
  // 获取当前用户信息
  async getProfile(req, res) {
    try {
      const user = await User.findByPk(req.user.id, {
        attributes: { exclude: ['password'] }
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      console.error('获取用户信息错误:', error);
      res.status(500).json({
        success: false,
        message: '获取用户信息失败'
      });
    }
  }

  // 更新当前用户信息
  async updateProfile(req, res) {
    try {
      const { real_name, phone, email, department, avatar } = req.body;
      const userId = req.user.id;

      // 检查手机号是否已被其他用户使用
      if (phone) {
        const existingUser = await User.findOne({
          where: {
            phone,
            id: { [Op.ne]: userId }
          }
        });

        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: '手机号已被使用'
          });
        }
      }

      // 检查邮箱是否已被其他用户使用
      if (email) {
        const existingUser = await User.findOne({
          where: {
            email,
            id: { [Op.ne]: userId }
          }
        });

        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: '邮箱已被使用'
          });
        }
      }

      await User.update({
        real_name,
        phone,
        email,
        department,
        avatar,
        updated_by: userId
      }, {
        where: { id: userId }
      });

      const updatedUser = await User.findByPk(userId, {
        attributes: { exclude: ['password'] }
      });

      res.json({
        success: true,
        message: '更新成功',
        data: updatedUser
      });
    } catch (error) {
      console.error('更新用户信息错误:', error);
      res.status(500).json({
        success: false,
        message: '更新失败'
      });
    }
  }

  // 获取用户列表（管理员）
  async getUsers(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        role,
        department,
        is_verified,
        is_active,
        keyword
      } = req.query;

      const offset = (page - 1) * limit;
      const where = {};

      // 构建查询条件
      if (role) {
        where.role = role;
      }

      if (department) {
        where.department = { [Op.like]: `%${department}%` };
      }

      if (is_verified !== undefined) {
        where.is_verified = is_verified === 'true';
      }

      if (is_active !== undefined) {
        where.is_active = is_active === 'true';
      }

      if (keyword) {
        where[Op.or] = [
          { username: { [Op.like]: `%${keyword}%` } },
          { real_name: { [Op.like]: `%${keyword}%` } },
          { phone: { [Op.like]: `%${keyword}%` } },
          { email: { [Op.like]: `%${keyword}%` } }
        ];
      }

      const { count, rows } = await User.findAndCountAll({
        where,
        attributes: { exclude: ['password'] },
        limit: parseInt(limit),
        offset,
        order: [['created_at', 'DESC']]
      });

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取用户列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取用户列表失败'
      });
    }
  }

  // 获取特定用户信息（管理员）
  async getUserById(req, res) {
    try {
      const { id } = req.params;

      const user = await User.findByPk(id, {
        attributes: { exclude: ['password'] }
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      console.error('获取用户信息错误:', error);
      res.status(500).json({
        success: false,
        message: '获取用户信息失败'
      });
    }
  }

  // 更新用户信息（管理员）
  async updateUser(req, res) {
    try {
      const { id } = req.params;
      const {
        real_name,
        phone,
        email,
        department,
        employee_id,
        is_verified,
        is_active,
        permissions
      } = req.body;

      const user = await User.findByPk(id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 检查手机号是否已被其他用户使用
      if (phone && phone !== user.phone) {
        const existingUser = await User.findOne({
          where: {
            phone,
            id: { [Op.ne]: id }
          }
        });

        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: '手机号已被使用'
          });
        }
      }

      // 检查邮箱是否已被其他用户使用
      if (email && email !== user.email) {
        const existingUser = await User.findOne({
          where: {
            email,
            id: { [Op.ne]: id }
          }
        });

        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: '邮箱已被使用'
          });
        }
      }

      await user.update({
        real_name,
        phone,
        email,
        department,
        employee_id,
        is_verified,
        is_active,
        permissions,
        updated_by: req.user.id
      });

      res.json({
        success: true,
        message: '更新成功',
        data: user.toJSON()
      });
    } catch (error) {
      console.error('更新用户信息错误:', error);
      res.status(500).json({
        success: false,
        message: '更新失败'
      });
    }
  }

  // 更新用户状态（管理员）
  async updateUserStatus(req, res) {
    try {
      const { id } = req.params;
      const { is_active } = req.body;

      const user = await User.findByPk(id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 不能禁用自己
      if (id == req.user.id) {
        return res.status(400).json({
          success: false,
          message: '不能禁用自己'
        });
      }

      await user.update({
        is_active,
        updated_by: req.user.id
      });

      res.json({
        success: true,
        message: is_active ? '用户已启用' : '用户已禁用'
      });
    } catch (error) {
      console.error('更新用户状态错误:', error);
      res.status(500).json({
        success: false,
        message: '更新状态失败'
      });
    }
  }

  // 分配角色（管理员）
  async assignRole(req, res) {
    try {
      const { id } = req.params;
      const { role } = req.body;

      const user = await User.findByPk(id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 只有超级管理员可以分配管理员角色
      if (role === 'admin' || role === 'super_admin') {
        if (req.user.role !== 'super_admin') {
          return res.status(403).json({
            success: false,
            message: '权限不足'
          });
        }
      }

      // 不能修改自己的角色
      if (id == req.user.id) {
        return res.status(400).json({
          success: false,
          message: '不能修改自己的角色'
        });
      }

      await user.update({
        role,
        updated_by: req.user.id
      });

      res.json({
        success: true,
        message: '角色分配成功'
      });
    } catch (error) {
      console.error('分配角色错误:', error);
      res.status(500).json({
        success: false,
        message: '角色分配失败'
      });
    }
  }

  // 获取用户统计信息
  async getStats(req, res) {
    try {
      const userId = req.user.id;

      // 这里应该根据用户角色返回不同的统计信息
      // 暂时返回模拟数据
      const stats = {
        totalEquipment: 0,
        pendingOrders: 0,
        completedOrders: 0,
        maintenanceEquipment: 0
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('获取用户统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取统计信息失败'
      });
    }
  }

  // 获取用户统计信息（兼容小程序API）
  async getUserStats(req, res) {
    try {
      const userId = req.user.id;

      // 模拟统计数据
      const stats = {
        myOrders: 12,
        myEquipment: 8,
        unreadMessages: 3
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('获取用户统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取统计信息失败'
      });
    }
  }

  // 获取认证状态
  async getVerifyStatus(req, res) {
    try {
      const user = await User.findByPk(req.user.id, {
        attributes: ['is_verified', 'real_name']
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      res.json({
        success: true,
        data: {
          isVerified: user.is_verified,
          status: user.is_verified ? 'verified' : 'pending',
          realName: user.real_name || '',
          idCard: '' // 出于安全考虑，不返回身份证号
        }
      });
    } catch (error) {
      console.error('获取认证状态错误:', error);
      res.status(500).json({
        success: false,
        message: '获取认证状态失败'
      });
    }
  }

  // 提交实名认证
  async submitVerification(req, res) {
    try {
      const { realName, idCard } = req.body;
      const userId = req.user.id;

      if (!realName || !idCard) {
        return res.status(400).json({
          success: false,
          message: '请提供真实姓名和身份证号'
        });
      }

      // 更新用户信息
      await User.update({
        real_name: realName,
        // 注意：实际应用中身份证号需要加密存储
        updated_by: userId
      }, {
        where: { id: userId }
      });

      res.json({
        success: true,
        message: '认证申请提交成功',
        data: {
          status: 'processing',
          submittedAt: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('提交实名认证错误:', error);
      res.status(500).json({
        success: false,
        message: '提交认证申请失败'
      });
    }
  }

  // 获取未读通知数量
  async getUnreadNotificationCount(req, res) {
    try {
      // 模拟未读通知数量
      const count = Math.floor(Math.random() * 10);

      res.json({
        success: true,
        data: {
          count
        }
      });
    } catch (error) {
      console.error('获取未读通知数量错误:', error);
      res.status(500).json({
        success: false,
        message: '获取未读通知数量失败'
      });
    }
  }
}

module.exports = new UserController();
