const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

class UploadController {
  constructor() {
    this.initializeStorage();
  }

  // 初始化存储配置
  initializeStorage() {
    // 确保上传目录存在
    const uploadDirs = [
      'uploads/images',
      'uploads/files',
      'uploads/avatars',
      'uploads/qrcodes'
    ];

    uploadDirs.forEach(dir => {
      const fullPath = path.join(__dirname, '../../', dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
      }
    });

    // 配置multer存储
    this.storage = multer.diskStorage({
      destination: (req, file, cb) => {
        let uploadPath = 'uploads/files'; // 默认路径
        
        if (req.path.includes('/image')) {
          uploadPath = 'uploads/images';
        } else if (req.path.includes('/avatar')) {
          uploadPath = 'uploads/avatars';
        }
        
        cb(null, path.join(__dirname, '../../', uploadPath));
      },
      filename: (req, file, cb) => {
        // 生成唯一文件名
        const ext = path.extname(file.originalname);
        const filename = `${uuidv4()}${ext}`;
        cb(null, filename);
      }
    });

    // 文件过滤器
    this.fileFilter = (req, file, cb) => {
      const allowedTypes = this.getAllowedTypes(req.path);
      const fileType = file.mimetype;
      
      if (allowedTypes.includes(fileType)) {
        cb(null, true);
      } else {
        cb(new Error(`不支持的文件类型: ${fileType}`), false);
      }
    };

    // 配置multer
    this.upload = multer({
      storage: this.storage,
      fileFilter: this.fileFilter,
      limits: {
        fileSize: this.getMaxFileSize(req?.path || ''),
        files: 10 // 最多10个文件
      }
    });
  }

  // 获取允许的文件类型
  getAllowedTypes(uploadPath) {
    if (uploadPath.includes('/image') || uploadPath.includes('/avatar')) {
      return [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp'
      ];
    } else {
      return [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'application/zip',
        'application/x-zip-compressed'
      ];
    }
  }

  // 获取最大文件大小
  getMaxFileSize(uploadPath) {
    if (uploadPath.includes('/image') || uploadPath.includes('/avatar')) {
      return 5 * 1024 * 1024; // 5MB for images
    } else {
      return 10 * 1024 * 1024; // 10MB for other files
    }
  }

  // 上传单个图片
  uploadImage = (req, res) => {
    const uploadSingle = this.upload.single('image');
    
    uploadSingle(req, res, (err) => {
      if (err) {
        console.error('图片上传错误:', err);
        return res.status(400).json({
          success: false,
          message: err.message || '图片上传失败'
        });
      }

      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '请选择要上传的图片'
        });
      }

      const fileUrl = `/uploads/images/${req.file.filename}`;
      
      res.json({
        success: true,
        message: '图片上传成功',
        data: {
          filename: req.file.filename,
          originalname: req.file.originalname,
          size: req.file.size,
          url: fileUrl,
          fullUrl: `${req.protocol}://${req.get('host')}${fileUrl}`
        }
      });
    });
  };

  // 上传多个图片
  uploadImages = (req, res) => {
    const uploadMultiple = this.upload.array('images', 6);
    
    uploadMultiple(req, res, (err) => {
      if (err) {
        console.error('图片上传错误:', err);
        return res.status(400).json({
          success: false,
          message: err.message || '图片上传失败'
        });
      }

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请选择要上传的图片'
        });
      }

      const files = req.files.map(file => {
        const fileUrl = `/uploads/images/${file.filename}`;
        return {
          filename: file.filename,
          originalname: file.originalname,
          size: file.size,
          url: fileUrl,
          fullUrl: `${req.protocol}://${req.get('host')}${fileUrl}`
        };
      });

      res.json({
        success: true,
        message: '图片上传成功',
        data: files
      });
    });
  };

  // 上传头像
  uploadAvatar = (req, res) => {
    const uploadSingle = this.upload.single('avatar');
    
    uploadSingle(req, res, (err) => {
      if (err) {
        console.error('头像上传错误:', err);
        return res.status(400).json({
          success: false,
          message: err.message || '头像上传失败'
        });
      }

      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '请选择要上传的头像'
        });
      }

      const fileUrl = `/uploads/avatars/${req.file.filename}`;
      
      res.json({
        success: true,
        message: '头像上传成功',
        data: {
          filename: req.file.filename,
          originalname: req.file.originalname,
          size: req.file.size,
          url: fileUrl,
          fullUrl: `${req.protocol}://${req.get('host')}${fileUrl}`
        }
      });
    });
  };

  // 上传文件
  uploadFile = (req, res) => {
    const uploadSingle = this.upload.single('file');
    
    uploadSingle(req, res, (err) => {
      if (err) {
        console.error('文件上传错误:', err);
        return res.status(400).json({
          success: false,
          message: err.message || '文件上传失败'
        });
      }

      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '请选择要上传的文件'
        });
      }

      const fileUrl = `/uploads/files/${req.file.filename}`;
      
      res.json({
        success: true,
        message: '文件上传成功',
        data: {
          filename: req.file.filename,
          originalname: req.file.originalname,
          size: req.file.size,
          mimetype: req.file.mimetype,
          url: fileUrl,
          fullUrl: `${req.protocol}://${req.get('host')}${fileUrl}`
        }
      });
    });
  };

  // 删除文件
  async deleteFile(req, res) {
    try {
      const { filename } = req.params;
      const { type = 'files' } = req.query;

      // 验证文件类型
      const allowedTypes = ['images', 'files', 'avatars', 'qrcodes'];
      if (!allowedTypes.includes(type)) {
        return res.status(400).json({
          success: false,
          message: '无效的文件类型'
        });
      }

      const filePath = path.join(__dirname, '../../uploads', type, filename);

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({
          success: false,
          message: '文件不存在'
        });
      }

      // 删除文件
      fs.unlinkSync(filePath);

      res.json({
        success: true,
        message: '文件删除成功'
      });
    } catch (error) {
      console.error('删除文件错误:', error);
      res.status(500).json({
        success: false,
        message: '删除文件失败'
      });
    }
  }

  // 获取文件信息
  async getFileInfo(req, res) {
    try {
      const { filename } = req.params;
      const { type = 'files' } = req.query;

      const filePath = path.join(__dirname, '../../uploads', type, filename);

      if (!fs.existsSync(filePath)) {
        return res.status(404).json({
          success: false,
          message: '文件不存在'
        });
      }

      const stats = fs.statSync(filePath);
      const fileUrl = `/uploads/${type}/${filename}`;

      res.json({
        success: true,
        data: {
          filename,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          url: fileUrl,
          fullUrl: `${req.protocol}://${req.get('host')}${fileUrl}`
        }
      });
    } catch (error) {
      console.error('获取文件信息错误:', error);
      res.status(500).json({
        success: false,
        message: '获取文件信息失败'
      });
    }
  }

  // 下载文件
  async downloadFile(req, res) {
    try {
      const { filename } = req.params;
      const { type = 'files' } = req.query;

      const filePath = path.join(__dirname, '../../uploads', type, filename);

      if (!fs.existsSync(filePath)) {
        return res.status(404).json({
          success: false,
          message: '文件不存在'
        });
      }

      // 设置下载头
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      
      // 发送文件
      res.sendFile(filePath);
    } catch (error) {
      console.error('下载文件错误:', error);
      res.status(500).json({
        success: false,
        message: '下载文件失败'
      });
    }
  }
}

module.exports = new UploadController();
