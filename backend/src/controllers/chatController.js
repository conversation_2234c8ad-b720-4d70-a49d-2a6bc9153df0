const { Chat, User, Order } = require('../models');
const { Op } = require('sequelize');
const socketService = require('../services/socketService');

class ChatController {
  // 发送消息
  async sendMessage(req, res) {
    try {
      const {
        order_id,
        message_type = 'text',
        content
      } = req.body;

      const senderId = req.user.id;

      // 验证工单是否存在
      const order = await Order.findByPk(order_id, {
        include: [
          { model: User, as: 'reporter', attributes: ['id'] },
          { model: User, as: 'engineer', attributes: ['id'] }
        ]
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      // 权限检查：只有工单相关人员可以发送消息
      const allowedUsers = [order.user_id];
      if (order.engineer_id) {
        allowedUsers.push(order.engineer_id);
      }

      if (!allowedUsers.includes(senderId) && !['admin', 'super_admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: '无权在此工单中发送消息'
        });
      }

      // 创建消息记录
      const message = await Chat.create({
        order_id,
        sender_id: senderId,
        message_type,
        content,
        is_read: false
      });

      // 获取完整的消息信息
      const fullMessage = await Chat.findByPk(message.id, {
        include: [
          { model: User, as: 'sender', attributes: ['id', 'username', 'real_name', 'avatar'] }
        ]
      });

      // 通过Socket.IO实时推送消息
      const roomName = `order_${order_id}`;
      socketService.broadcastToRoom(roomName, 'new_message', {
        message: fullMessage,
        orderId: order_id
      });

      // 发送通知给其他参与者
      const otherUsers = allowedUsers.filter(userId => userId !== senderId);
      otherUsers.forEach(userId => {
        socketService.sendNotificationToUser(userId, {
          type: 'new_message',
          title: '新消息',
          content: message_type === 'text' ? content : `[${this.getMessageTypeText(message_type)}]`,
          orderId: order_id,
          messageId: message.id
        });
      });

      res.status(201).json({
        success: true,
        message: '消息发送成功',
        data: fullMessage
      });
    } catch (error) {
      console.error('发送消息错误:', error);
      res.status(500).json({
        success: false,
        message: '发送消息失败'
      });
    }
  }

  // 获取消息列表
  async getMessages(req, res) {
    try {
      const {
        order_id,
        page = 1,
        limit = 50
      } = req.query;

      const userId = req.user.id;
      const offset = (page - 1) * limit;

      // 验证工单权限
      const order = await Order.findByPk(order_id);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      // 权限检查
      const allowedUsers = [order.user_id];
      if (order.engineer_id) {
        allowedUsers.push(order.engineer_id);
      }

      if (!allowedUsers.includes(userId) && !['admin', 'super_admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: '无权查看此工单的消息'
        });
      }

      const { count, rows } = await Chat.findAndCountAll({
        where: { order_id },
        include: [
          { model: User, as: 'sender', attributes: ['id', 'username', 'real_name', 'avatar'] }
        ],
        limit: parseInt(limit),
        offset,
        order: [['created_at', 'ASC']]
      });

      // 标记消息为已读（除了自己发送的消息）
      await Chat.update(
        { is_read: true },
        {
          where: {
            order_id,
            sender_id: { [Op.ne]: userId },
            is_read: false
          }
        }
      );

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取消息列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取消息列表失败'
      });
    }
  }

  // 获取聊天列表（用户的所有聊天）
  async getChatList(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 20 } = req.query;
      const offset = (page - 1) * limit;

      // 获取用户参与的工单
      let orderWhere = {};
      if (req.user.role === 'engineer') {
        orderWhere.engineer_id = userId;
      } else if (req.user.role === 'user') {
        orderWhere.user_id = userId;
      }
      // 管理员可以看到所有聊天

      const orders = await Order.findAll({
        where: orderWhere,
        include: [
          { model: User, as: 'reporter', attributes: ['id', 'username', 'real_name', 'avatar'] },
          { model: User, as: 'engineer', attributes: ['id', 'username', 'real_name', 'avatar'] }
        ],
        limit: parseInt(limit),
        offset,
        order: [['updated_at', 'DESC']]
      });

      // 获取每个工单的最新消息和未读数量
      const chatList = await Promise.all(
        orders.map(async (order) => {
          const [lastMessage, unreadCount] = await Promise.all([
            Chat.findOne({
              where: { order_id: order.id },
              include: [
                { model: User, as: 'sender', attributes: ['id', 'username', 'real_name'] }
              ],
              order: [['created_at', 'DESC']]
            }),
            Chat.count({
              where: {
                order_id: order.id,
                sender_id: { [Op.ne]: userId },
                is_read: false
              }
            })
          ]);

          return {
            order: {
              id: order.id,
              order_number: order.order_number,
              title: order.title,
              status: order.status,
              reporter: order.reporter,
              engineer: order.engineer
            },
            lastMessage: lastMessage ? {
              content: lastMessage.message_type === 'text' ? lastMessage.content : `[${this.getMessageTypeText(lastMessage.message_type)}]`,
              created_at: lastMessage.created_at,
              sender: lastMessage.sender
            } : null,
            unreadCount
          };
        })
      );

      res.json({
        success: true,
        data: {
          list: chatList,
          page: parseInt(page),
          limit: parseInt(limit)
        }
      });
    } catch (error) {
      console.error('获取聊天列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取聊天列表失败'
      });
    }
  }

  // 标记消息为已读
  async markAsRead(req, res) {
    try {
      const { order_id } = req.body;
      const userId = req.user.id;

      // 验证工单权限
      const order = await Order.findByPk(order_id);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      const allowedUsers = [order.user_id];
      if (order.engineer_id) {
        allowedUsers.push(order.engineer_id);
      }

      if (!allowedUsers.includes(userId) && !['admin', 'super_admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: '无权操作此工单的消息'
        });
      }

      // 标记消息为已读
      await Chat.update(
        { is_read: true },
        {
          where: {
            order_id,
            sender_id: { [Op.ne]: userId },
            is_read: false
          }
        }
      );

      res.json({
        success: true,
        message: '消息已标记为已读'
      });
    } catch (error) {
      console.error('标记消息已读错误:', error);
      res.status(500).json({
        success: false,
        message: '标记消息已读失败'
      });
    }
  }

  // 删除消息
  async deleteMessage(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const message = await Chat.findByPk(id);
      if (!message) {
        return res.status(404).json({
          success: false,
          message: '消息不存在'
        });
      }

      // 只有消息发送者或管理员可以删除消息
      if (message.sender_id !== userId && !['admin', 'super_admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: '无权删除此消息'
        });
      }

      await message.destroy();

      // 通知其他用户消息已删除
      const roomName = `order_${message.order_id}`;
      socketService.broadcastToRoom(roomName, 'message_deleted', {
        messageId: id,
        orderId: message.order_id
      });

      res.json({
        success: true,
        message: '消息删除成功'
      });
    } catch (error) {
      console.error('删除消息错误:', error);
      res.status(500).json({
        success: false,
        message: '删除消息失败'
      });
    }
  }

  // 获取未读消息数量
  async getUnreadCount(req, res) {
    try {
      const userId = req.user.id;

      // 获取用户参与的工单
      let orderWhere = {};
      if (req.user.role === 'engineer') {
        orderWhere.engineer_id = userId;
      } else if (req.user.role === 'user') {
        orderWhere.user_id = userId;
      }

      const orders = await Order.findAll({
        where: orderWhere,
        attributes: ['id']
      });

      const orderIds = orders.map(order => order.id);

      const unreadCount = await Chat.count({
        where: {
          order_id: { [Op.in]: orderIds },
          sender_id: { [Op.ne]: userId },
          is_read: false
        }
      });

      res.json({
        success: true,
        data: { unreadCount }
      });
    } catch (error) {
      console.error('获取未读消息数量错误:', error);
      res.status(500).json({
        success: false,
        message: '获取未读消息数量失败'
      });
    }
  }

  // 获取消息类型文本
  getMessageTypeText(messageType) {
    const typeMap = {
      'text': '文字',
      'image': '图片',
      'voice': '语音',
      'file': '文件',
      'video': '视频'
    };
    return typeMap[messageType] || '消息';
  }

  // 加入聊天室（Socket.IO）
  async joinChatRoom(req, res) {
    try {
      const { order_id } = req.body;
      const userId = req.user.id;

      // 验证工单权限
      const order = await Order.findByPk(order_id);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      const allowedUsers = [order.user_id];
      if (order.engineer_id) {
        allowedUsers.push(order.engineer_id);
      }

      if (!allowedUsers.includes(userId) && !['admin', 'super_admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: '无权加入此聊天室'
        });
      }

      const roomName = `order_${order_id}`;
      
      res.json({
        success: true,
        message: '加入聊天室成功',
        data: { roomName }
      });
    } catch (error) {
      console.error('加入聊天室错误:', error);
      res.status(500).json({
        success: false,
        message: '加入聊天室失败'
      });
    }
  }
}

module.exports = new ChatController();
