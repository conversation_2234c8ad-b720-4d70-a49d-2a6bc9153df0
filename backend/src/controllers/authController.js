const jwt = require('jsonwebtoken');
const { User, UserRole, ResearchGroup } = require('../models');
const axios = require('axios');

class AuthController {
  // 微信小程序登录
  async wechatLogin(req, res) {
    try {
      const { code, userInfo } = req.body;
      
      // 调用微信API获取openid和session_key
      const wechatResponse = await this.getWechatUserInfo(code);
      
      if (!wechatResponse.openid) {
        return res.status(400).json({
          success: false,
          message: '微信登录失败'
        });
      }

      const { openid, session_key, unionid } = wechatResponse;

      // 查找或创建用户
      let user = await User.findByOpenid(openid);
      
      if (!user) {
        // 创建新用户
        user = await User.create({
          openid,
          unionid,
          username: userInfo?.nickName || `用户${Date.now()}`,
          avatar: userInfo?.avatarUrl,
          role: 'user'
        });

        // 为新用户创建默认角色
        await UserRole.create({
          user_id: user.id,
          role: 'user',
          role_name: '普通用户',
          is_default: true,
          is_active: true,
          granted_at: new Date()
        });
      } else {
        // 更新用户信息
        await user.update({
          username: userInfo?.nickName || user.username,
          avatar: userInfo?.avatarUrl || user.avatar,
          last_login_at: new Date()
        });
      }

      // 获取用户角色信息
      const userRoles = await UserRole.findByUserId(user.id);
      const defaultRole = await UserRole.getUserDefaultRole(user.id);

      const roles = userRoles.map(ur => ur.role);
      const currentRole = defaultRole ? defaultRole.role : 'user';
      const isMultiRole = userRoles.length > 1;
      const needRoleSelection = isMultiRole;

      // 生成JWT token，包含角色信息
      const tokenPayload = {
        userId: user.id,
        currentRole,
        roleId: defaultRole ? defaultRole.id : null,
        researchGroupId: defaultRole ? defaultRole.research_group_id : null,
        permissions: defaultRole ? (defaultRole.permissions || []) : []
      };

      const token = this.generateToken(tokenPayload);
      const refreshToken = this.generateRefreshToken(user.id);

      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: {
            ...user.toJSON(),
            roles,
            currentRole,
            isMultiRole
          },
          token,
          refreshToken,
          needRoleSelection
        }
      });

    } catch (error) {
      console.error('微信登录错误:', error);
      res.status(500).json({
        success: false,
        message: '登录失败'
      });
    }
  }

  // 刷新token
  async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(400).json({
          success: false,
          message: '刷新令牌缺失'
        });
      }

      const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET);
      const user = await User.findByPk(decoded.userId);

      if (!user || !user.is_active) {
        return res.status(401).json({
          success: false,
          message: '用户不存在或已禁用'
        });
      }

      const newToken = this.generateToken(user.id);
      const newRefreshToken = this.generateRefreshToken(user.id);

      res.json({
        success: true,
        message: '令牌刷新成功',
        data: {
          token: newToken,
          refreshToken: newRefreshToken
        }
      });

    } catch (error) {
      console.error('刷新令牌错误:', error);
      res.status(401).json({
        success: false,
        message: '令牌刷新失败'
      });
    }
  }

  // 登出
  async logout(req, res) {
    try {
      // 这里可以实现token黑名单机制
      res.json({
        success: true,
        message: '登出成功'
      });
    } catch (error) {
      console.error('登出错误:', error);
      res.status(500).json({
        success: false,
        message: '登出失败'
      });
    }
  }

  // 实名认证
  async verifyIdentity(req, res) {
    try {
      const { realName, idCard, phone, department, employeeId } = req.body;
      const userId = req.user.id;

      // 验证身份证号格式
      if (!this.validateIdCard(idCard)) {
        return res.status(400).json({
          success: false,
          message: '身份证号格式不正确'
        });
      }

      // 验证手机号格式
      if (!this.validatePhone(phone)) {
        return res.status(400).json({
          success: false,
          message: '手机号格式不正确'
        });
      }

      // 更新用户信息
      await User.update({
        real_name: realName,
        id_card: idCard,
        phone,
        department,
        employee_id: employeeId,
        is_verified: true
      }, {
        where: { id: userId }
      });

      res.json({
        success: true,
        message: '实名认证成功'
      });

    } catch (error) {
      console.error('实名认证错误:', error);
      res.status(500).json({
        success: false,
        message: '实名认证失败'
      });
    }
  }

  // 获取微信用户信息
  async getWechatUserInfo(code) {
    try {
      const appId = process.env.WECHAT_APP_ID;
      const appSecret = process.env.WECHAT_APP_SECRET;
      
      const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;
      
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error('获取微信用户信息错误:', error);
      throw error;
    }
  }

  // 生成访问token
  generateToken(payload) {
    // 如果传入的是数字，则为旧版本兼容
    if (typeof payload === 'number') {
      payload = { userId: payload };
    }

    return jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );
  }

  // 生成刷新token
  generateRefreshToken(userId) {
    return jwt.sign(
      { userId, type: 'refresh' },
      process.env.JWT_SECRET,
      { expiresIn: '30d' }
    );
  }

  // 验证身份证号
  validateIdCard(idCard) {
    const pattern = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return pattern.test(idCard);
  }

  // 验证手机号
  validatePhone(phone) {
    const pattern = /^1[3-9]\d{9}$/;
    return pattern.test(phone);
  }
}

module.exports = new AuthController();
