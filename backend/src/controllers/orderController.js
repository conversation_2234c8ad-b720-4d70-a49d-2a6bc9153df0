const { Order, Equipment, User, OrderLog } = require('../models');
const { Op } = require('sequelize');
const socketService = require('../services/socketService');

class OrderController {
  // 创建工单
  async createOrder(req, res) {
    try {
      const {
        equipment_id,
        title,
        description,
        fault_type,
        priority = 'normal',
        images,
        contact_phone,
        contact_email,
        expected_date
      } = req.body;

      const userId = req.user.id;

      // 验证设备是否存在
      const equipment = await Equipment.findByPk(equipment_id);
      if (!equipment) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      // 生成工单号
      const orderNumber = Order.generateOrderNumber();

      // 创建工单
      const order = await Order.create({
        order_number: orderNumber,
        equipment_id,
        user_id: userId,
        title,
        description,
        fault_type,
        priority,
        images,
        contact_phone,
        contact_email,
        expected_date,
        status: 'pending',
        created_by: userId
      });

      // 记录操作日志
      await OrderLog.create({
        order_id: order.id,
        user_id: userId,
        action: 'create',
        description: '创建工单',
        new_status: 'pending'
      });

      // 获取完整的工单信息
      const fullOrder = await Order.findByPk(order.id, {
        include: [
          { model: Equipment, as: 'equipment' },
          { model: User, as: 'reporter', attributes: ['id', 'username', 'real_name'] }
        ]
      });

      // 通知管理员有新工单
      socketService.broadcastSystemNotification({
        type: 'order_created',
        title: '新工单创建',
        content: `用户 ${req.user.real_name || req.user.username} 创建了新工单：${title}`,
        orderId: order.id
      }, ['admin', 'super_admin']);

      res.status(201).json({
        success: true,
        message: '工单创建成功',
        data: fullOrder
      });
    } catch (error) {
      console.error('创建工单错误:', error);
      res.status(500).json({
        success: false,
        message: '创建工单失败'
      });
    }
  }

  // 获取工单列表
  async getOrders(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        priority,
        keyword,
        timeRange,
        sort = 'created_at',
        order = 'desc'
      } = req.query;

      const offset = (page - 1) * limit;
      const where = {};

      // 构建查询条件
      if (status) {
        where.status = status;
      }

      if (priority) {
        where.priority = priority;
      }

      if (keyword) {
        where[Op.or] = [
          { order_number: { [Op.like]: `%${keyword}%` } },
          { title: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } }
        ];
      }

      // 时间范围筛选
      if (timeRange) {
        const now = new Date();
        let startDate;

        switch (timeRange) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
        }

        if (startDate) {
          where.created_at = {
            [Op.gte]: startDate
          };
        }
      }

      // 根据用户角色过滤
      if (req.user.role === 'user') {
        where.user_id = req.user.id;
      }

      const { count, rows } = await Order.findAndCountAll({
        where,
        include: [
          { model: Equipment, as: 'equipment' },
          { model: User, as: 'reporter', attributes: ['id', 'username', 'real_name', 'department'] },
          { model: User, as: 'engineer', attributes: ['id', 'username', 'real_name'] }
        ],
        limit: parseInt(limit),
        offset,
        order: [[sort, order.toUpperCase()]]
      });

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取工单列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取工单列表失败'
      });
    }
  }

  // 获取我的工单
  async getMyOrders(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        sort = 'created_at',
        order = 'desc'
      } = req.query;

      const offset = (page - 1) * limit;
      const where = { user_id: req.user.id };

      if (status) {
        where.status = status;
      }

      const { count, rows } = await Order.findAndCountAll({
        where,
        include: [
          { model: Equipment, as: 'equipment' },
          { model: User, as: 'engineer', attributes: ['id', 'username', 'real_name'] }
        ],
        limit: parseInt(limit),
        offset,
        order: [[sort, order.toUpperCase()]]
      });

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取我的工单错误:', error);
      res.status(500).json({
        success: false,
        message: '获取我的工单失败'
      });
    }
  }

  // 获取分配给我的工单（工程师）
  async getAssignedOrders(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        priority,
        sort = 'created_at',
        order = 'desc'
      } = req.query;

      const offset = (page - 1) * limit;
      const where = { engineer_id: req.user.id };

      if (status) {
        where.status = status;
      }

      if (priority) {
        where.priority = priority;
      }

      const { count, rows } = await Order.findAndCountAll({
        where,
        include: [
          { model: Equipment, as: 'equipment' },
          { model: User, as: 'reporter', attributes: ['id', 'username', 'real_name', 'department'] }
        ],
        limit: parseInt(limit),
        offset,
        order: [[sort, order.toUpperCase()]]
      });

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取分配工单错误:', error);
      res.status(500).json({
        success: false,
        message: '获取分配工单失败'
      });
    }
  }

  // 获取工单详情
  async getOrderById(req, res) {
    try {
      const { id } = req.params;

      const order = await Order.findByPk(id, {
        include: [
          { model: Equipment, as: 'equipment' },
          { model: User, as: 'reporter', attributes: ['id', 'username', 'real_name', 'phone', 'email', 'department'] },
          { model: User, as: 'engineer', attributes: ['id', 'username', 'real_name', 'phone'] }
        ]
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      // 权限检查
      if (req.user.role === 'user' && order.user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '无权访问此工单'
        });
      }

      if (req.user.role === 'engineer' && order.engineer_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '无权访问此工单'
        });
      }

      res.json({
        success: true,
        data: order
      });
    } catch (error) {
      console.error('获取工单详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取工单详情失败'
      });
    }
  }

  // 分配工单（管理员）
  async assignOrder(req, res) {
    try {
      const { id } = req.params;
      const { engineer_id } = req.body;

      const order = await Order.findByPk(id);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      // 验证工程师是否存在
      const engineer = await User.findOne({
        where: {
          id: engineer_id,
          role: 'engineer',
          is_active: true
        }
      });

      if (!engineer) {
        return res.status(404).json({
          success: false,
          message: '工程师不存在或已禁用'
        });
      }

      const oldStatus = order.status;
      await order.update({
        engineer_id,
        status: 'assigned',
        assigned_at: new Date(),
        updated_by: req.user.id
      });

      // 记录操作日志
      await OrderLog.create({
        order_id: order.id,
        user_id: req.user.id,
        action: 'assign',
        description: `分配工程师：${engineer.real_name || engineer.username}`,
        old_status: oldStatus,
        new_status: 'assigned'
      });

      // 通知工程师
      socketService.sendNotificationToUser(engineer_id, {
        type: 'order_assigned',
        title: '新工单分配',
        content: `您有新的工单需要处理：${order.title}`,
        orderId: order.id
      });

      // 通知用户
      socketService.sendNotificationToUser(order.user_id, {
        type: 'order_assigned',
        title: '工单已分配',
        content: `您的工单已分配给工程师：${engineer.real_name || engineer.username}`,
        orderId: order.id
      });

      res.json({
        success: true,
        message: '工单分配成功'
      });
    } catch (error) {
      console.error('分配工单错误:', error);
      res.status(500).json({
        success: false,
        message: '分配工单失败'
      });
    }
  }

  // 更新工单状态
  async updateOrderStatus(req, res) {
    try {
      const { id } = req.params;
      const { status, notes } = req.body;

      const order = await Order.findByPk(id);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      // 权限检查
      if (req.user.role === 'engineer' && order.engineer_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '无权操作此工单'
        });
      }

      const oldStatus = order.status;
      const updateData = {
        status,
        updated_by: req.user.id
      };

      // 根据状态更新相应的时间字段
      if (status === 'in_progress' && !order.started_at) {
        updateData.started_at = new Date();
      }

      if (status === 'completed' && !order.completed_at) {
        updateData.completed_at = new Date();
      }

      await order.update(updateData);

      // 记录操作日志
      await OrderLog.create({
        order_id: order.id,
        user_id: req.user.id,
        action: 'update_status',
        description: notes || `状态更新：${oldStatus} -> ${status}`,
        old_status: oldStatus,
        new_status: status
      });

      // 通知相关用户
      socketService.sendNotificationToOrder(order.id, {
        type: 'order_status_changed',
        title: '工单状态更新',
        content: `工单状态已更新为：${this.getStatusText(status)}`,
        orderId: order.id,
        status
      });

      res.json({
        success: true,
        message: '状态更新成功'
      });
    } catch (error) {
      console.error('更新工单状态错误:', error);
      res.status(500).json({
        success: false,
        message: '更新状态失败'
      });
    }
  }

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待处理',
      'confirmed': '已确认',
      'assigned': '已分配',
      'in_progress': '维修中',
      'waiting_parts': '待配件',
      'testing': '测试中',
      'completed': '已完成',
      'cancelled': '已取消',
      'closed': '已关闭'
    };
    return statusMap[status] || status;
  }

  // 取消工单
  async cancelOrder(req, res) {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      const order = await Order.findByPk(id);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      // 只有工单创建者或管理员可以取消工单
      if (req.user.role === 'user' && order.user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '无权取消此工单'
        });
      }

      // 已完成的工单不能取消
      if (['completed', 'closed', 'cancelled'].includes(order.status)) {
        return res.status(400).json({
          success: false,
          message: '该工单无法取消'
        });
      }

      const oldStatus = order.status;
      await order.update({
        status: 'cancelled',
        internal_notes: reason,
        updated_by: req.user.id
      });

      // 记录操作日志
      await OrderLog.create({
        order_id: order.id,
        user_id: req.user.id,
        action: 'cancel',
        description: `取消工单：${reason}`,
        old_status: oldStatus,
        new_status: 'cancelled'
      });

      // 通知相关用户
      socketService.sendNotificationToOrder(order.id, {
        type: 'order_cancelled',
        title: '工单已取消',
        content: `工单已被取消，原因：${reason}`,
        orderId: order.id
      });

      res.json({
        success: true,
        message: '工单取消成功'
      });
    } catch (error) {
      console.error('取消工单错误:', error);
      res.status(500).json({
        success: false,
        message: '取消工单失败'
      });
    }
  }

  // 完成工单（工程师）
  async completeOrder(req, res) {
    try {
      const { id } = req.params;
      const {
        solution,
        fault_cause,
        replaced_parts,
        work_summary,
        actual_cost,
        parts_cost,
        labor_cost
      } = req.body;

      const order = await Order.findByPk(id);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      // 权限检查
      if (order.engineer_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '无权操作此工单'
        });
      }

      const oldStatus = order.status;
      await order.update({
        status: 'completed',
        solution,
        fault_cause,
        replaced_parts,
        work_summary,
        actual_cost,
        parts_cost,
        labor_cost,
        completed_at: new Date(),
        updated_by: req.user.id
      });

      // 记录操作日志
      await OrderLog.create({
        order_id: order.id,
        user_id: req.user.id,
        action: 'complete',
        description: '完成维修',
        old_status: oldStatus,
        new_status: 'completed'
      });

      // 通知用户
      socketService.sendNotificationToUser(order.user_id, {
        type: 'order_completed',
        title: '工单已完成',
        content: '您的维修工单已完成，请查看维修报告',
        orderId: order.id
      });

      res.json({
        success: true,
        message: '工单完成成功'
      });
    } catch (error) {
      console.error('完成工单错误:', error);
      res.status(500).json({
        success: false,
        message: '完成工单失败'
      });
    }
  }

  // 评价工单
  async rateOrder(req, res) {
    try {
      const { id } = req.params;
      const { rating, feedback } = req.body;

      const order = await Order.findByPk(id);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      // 只有工单创建者可以评价
      if (order.user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '无权评价此工单'
        });
      }

      // 只有已完成的工单可以评价
      if (order.status !== 'completed') {
        return res.status(400).json({
          success: false,
          message: '只能评价已完成的工单'
        });
      }

      await order.update({
        rating,
        feedback,
        updated_by: req.user.id
      });

      // 记录操作日志
      await OrderLog.create({
        order_id: order.id,
        user_id: req.user.id,
        action: 'rate',
        description: `评价工单：${rating}分`,
        new_data: { rating, feedback }
      });

      res.json({
        success: true,
        message: '评价成功'
      });
    } catch (error) {
      console.error('评价工单错误:', error);
      res.status(500).json({
        success: false,
        message: '评价失败'
      });
    }
  }

  // 获取工单操作日志
  async getOrderLogs(req, res) {
    try {
      const { id } = req.params;

      const order = await Order.findByPk(id);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在'
        });
      }

      const logs = await OrderLog.findAll({
        where: { order_id: id },
        include: [
          { model: User, as: 'operator', attributes: ['id', 'username', 'real_name'] }
        ],
        order: [['created_at', 'ASC']]
      });

      res.json({
        success: true,
        data: logs
      });
    } catch (error) {
      console.error('获取工单日志错误:', error);
      res.status(500).json({
        success: false,
        message: '获取工单日志失败'
      });
    }
  }
}

module.exports = new OrderController();
