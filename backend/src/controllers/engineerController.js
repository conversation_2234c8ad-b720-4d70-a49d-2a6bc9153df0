const { User, Order, Part, PartUsage, Equipment } = require('../models');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

class EngineerController {
  // 获取工程师列表（管理员）
  async getEngineers(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        department,
        is_active,
        keyword
      } = req.query;

      const offset = (page - 1) * limit;
      const where = { role: 'engineer' };

      if (department) {
        where.department = { [Op.like]: `%${department}%` };
      }

      if (is_active !== undefined) {
        where.is_active = is_active === 'true';
      }

      if (keyword) {
        where[Op.or] = [
          { username: { [Op.like]: `%${keyword}%` } },
          { real_name: { [Op.like]: `%${keyword}%` } },
          { phone: { [Op.like]: `%${keyword}%` } }
        ];
      }

      const { count, rows } = await User.findAndCountAll({
        where,
        attributes: { exclude: ['password'] },
        limit: parseInt(limit),
        offset,
        order: [['created_at', 'DESC']]
      });

      // 获取每个工程师的工单统计
      const engineersWithStats = await Promise.all(
        rows.map(async (engineer) => {
          const stats = await this.getEngineerOrderStats(engineer.id);
          return {
            ...engineer.toJSON(),
            stats
          };
        })
      );

      res.json({
        success: true,
        data: {
          list: engineersWithStats,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取工程师列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取工程师列表失败'
      });
    }
  }

  // 获取工程师工作统计
  async getEngineerStats(req, res) {
    try {
      const engineerId = req.user.id;
      const stats = await this.getEngineerOrderStats(engineerId);

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('获取工程师统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取统计信息失败'
      });
    }
  }

  // 获取工程师工单统计（内部方法）
  async getEngineerOrderStats(engineerId) {
    try {
      const [
        totalOrders,
        pendingOrders,
        inProgressOrders,
        completedOrders,
        avgRating,
        avgResponseTime
      ] = await Promise.all([
        // 总工单数
        Order.count({
          where: { engineer_id: engineerId }
        }),
        // 待处理工单数
        Order.count({
          where: {
            engineer_id: engineerId,
            status: ['assigned', 'pending']
          }
        }),
        // 进行中工单数
        Order.count({
          where: {
            engineer_id: engineerId,
            status: ['in_progress', 'waiting_parts', 'testing']
          }
        }),
        // 已完成工单数
        Order.count({
          where: {
            engineer_id: engineerId,
            status: 'completed'
          }
        }),
        // 平均评分
        Order.findOne({
          where: {
            engineer_id: engineerId,
            rating: { [Op.not]: null }
          },
          attributes: [
            [sequelize.fn('AVG', sequelize.col('rating')), 'avgRating']
          ]
        }),
        // 平均响应时间
        Order.findOne({
          where: {
            engineer_id: engineerId,
            response_time: { [Op.not]: null }
          },
          attributes: [
            [sequelize.fn('AVG', sequelize.col('response_time')), 'avgResponseTime']
          ]
        })
      ]);

      return {
        totalOrders: totalOrders || 0,
        pendingOrders: pendingOrders || 0,
        inProgressOrders: inProgressOrders || 0,
        completedOrders: completedOrders || 0,
        avgRating: avgRating?.dataValues?.avgRating ? parseFloat(avgRating.dataValues.avgRating).toFixed(1) : 0,
        avgResponseTime: avgResponseTime?.dataValues?.avgResponseTime ? Math.round(avgResponseTime.dataValues.avgResponseTime) : 0,
        statusCounts: {
          total: totalOrders || 0,
          pending: pendingOrders || 0,
          assigned: pendingOrders || 0,
          in_progress: inProgressOrders || 0,
          completed: completedOrders || 0
        }
      };
    } catch (error) {
      console.error('获取工程师统计错误:', error);
      return {
        totalOrders: 0,
        pendingOrders: 0,
        inProgressOrders: 0,
        completedOrders: 0,
        avgRating: 0,
        avgResponseTime: 0,
        statusCounts: {
          total: 0,
          pending: 0,
          assigned: 0,
          in_progress: 0,
          completed: 0
        }
      };
    }
  }

  // 获取工程师工作负载（管理员）
  async getEngineersWorkload(req, res) {
    try {
      const engineers = await User.findAll({
        where: {
          role: 'engineer',
          is_active: true
        },
        attributes: ['id', 'username', 'real_name', 'department']
      });

      const workloadData = await Promise.all(
        engineers.map(async (engineer) => {
          const [activeOrders, completedThisMonth] = await Promise.all([
            Order.count({
              where: {
                engineer_id: engineer.id,
                status: ['assigned', 'in_progress', 'waiting_parts', 'testing']
              }
            }),
            Order.count({
              where: {
                engineer_id: engineer.id,
                status: 'completed',
                completed_at: {
                  [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
                }
              }
            })
          ]);

          return {
            ...engineer.toJSON(),
            activeOrders,
            completedThisMonth,
            workload: this.calculateWorkload(activeOrders)
          };
        })
      );

      res.json({
        success: true,
        data: workloadData
      });
    } catch (error) {
      console.error('获取工程师工作负载错误:', error);
      res.status(500).json({
        success: false,
        message: '获取工作负载失败'
      });
    }
  }

  // 计算工作负载等级
  calculateWorkload(activeOrders) {
    if (activeOrders >= 10) return 'high';
    if (activeOrders >= 5) return 'medium';
    return 'low';
  }

  // 更新工程师状态
  async updateStatus(req, res) {
    try {
      const { status } = req.body; // online, offline, busy
      const engineerId = req.user.id;

      // 这里可以更新工程师的在线状态
      // 暂时只返回成功响应
      res.json({
        success: true,
        message: '状态更新成功'
      });
    } catch (error) {
      console.error('更新工程师状态错误:', error);
      res.status(500).json({
        success: false,
        message: '状态更新失败'
      });
    }
  }

  // 申请配件
  async requestParts(req, res) {
    try {
      const { order_id, parts } = req.body; // parts: [{ part_id, quantity, notes }]
      const engineerId = req.user.id;

      // 验证工单是否属于该工程师
      const order = await Order.findOne({
        where: {
          id: order_id,
          engineer_id: engineerId
        }
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在或无权操作'
        });
      }

      // 批量创建配件使用记录
      const partUsages = await Promise.all(
        parts.map(async (partData) => {
          const part = await Part.findByPk(partData.part_id);
          if (!part) {
            throw new Error(`配件 ${partData.part_id} 不存在`);
          }

          return PartUsage.create({
            order_id,
            part_id: partData.part_id,
            quantity: partData.quantity,
            unit_price: part.unit_price,
            total_price: part.unit_price * partData.quantity,
            usage_type: 'replacement',
            notes: partData.notes,
            used_by: engineerId,
            status: 'pending'
          });
        })
      );

      res.json({
        success: true,
        message: '配件申请提交成功',
        data: partUsages
      });
    } catch (error) {
      console.error('申请配件错误:', error);
      res.status(500).json({
        success: false,
        message: error.message || '申请配件失败'
      });
    }
  }

  // 获取配件申请记录
  async getPartRequests(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        order_id
      } = req.query;

      const offset = (page - 1) * limit;
      const where = { used_by: req.user.id };

      if (status) {
        where.status = status;
      }

      if (order_id) {
        where.order_id = order_id;
      }

      const { count, rows } = await PartUsage.findAndCountAll({
        where,
        include: [
          { model: Part, as: 'part' },
          { model: Order, as: 'order', attributes: ['id', 'order_number', 'title'] }
        ],
        limit: parseInt(limit),
        offset,
        order: [['created_at', 'DESC']]
      });

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取配件申请记录错误:', error);
      res.status(500).json({
        success: false,
        message: '获取配件申请记录失败'
      });
    }
  }

  // 领用配件
  async useParts(req, res) {
    try {
      const { usage_ids } = req.body; // 配件使用记录ID数组

      // 验证所有记录都属于该工程师且状态为已审批
      const usages = await PartUsage.findAll({
        where: {
          id: usage_ids,
          used_by: req.user.id,
          status: 'approved'
        },
        include: [{ model: Part, as: 'part' }]
      });

      if (usages.length !== usage_ids.length) {
        return res.status(400).json({
          success: false,
          message: '部分配件申请记录不存在或未审批'
        });
      }

      // 检查库存是否充足
      for (const usage of usages) {
        if (usage.part.stock_quantity < usage.quantity) {
          return res.status(400).json({
            success: false,
            message: `配件 ${usage.part.name} 库存不足`
          });
        }
      }

      // 开始事务
      const transaction = await sequelize.transaction();

      try {
        // 更新配件使用记录状态
        await PartUsage.update(
          { status: 'used' },
          {
            where: { id: usage_ids },
            transaction
          }
        );

        // 减少库存
        for (const usage of usages) {
          await Part.update(
            {
              stock_quantity: sequelize.literal(`stock_quantity - ${usage.quantity}`)
            },
            {
              where: { id: usage.part_id },
              transaction
            }
          );
        }

        await transaction.commit();

        res.json({
          success: true,
          message: '配件领用成功'
        });
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      console.error('领用配件错误:', error);
      res.status(500).json({
        success: false,
        message: '领用配件失败'
      });
    }
  }

  // 获取配件使用记录
  async getPartUsages(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        order_id,
        part_id
      } = req.query;

      const offset = (page - 1) * limit;
      const where = { used_by: req.user.id };

      if (order_id) {
        where.order_id = order_id;
      }

      if (part_id) {
        where.part_id = part_id;
      }

      const { count, rows } = await PartUsage.findAndCountAll({
        where,
        include: [
          { model: Part, as: 'part' },
          { model: Order, as: 'order', attributes: ['id', 'order_number', 'title'] }
        ],
        limit: parseInt(limit),
        offset,
        order: [['created_at', 'DESC']]
      });

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取配件使用记录错误:', error);
      res.status(500).json({
        success: false,
        message: '获取配件使用记录失败'
      });
    }
  }

  // 提交工作报告
  async submitReport(req, res) {
    try {
      const {
        order_id,
        work_summary,
        time_spent,
        issues_encountered,
        recommendations
      } = req.body;

      const engineerId = req.user.id;

      // 验证工单是否属于该工程师
      const order = await Order.findOne({
        where: {
          id: order_id,
          engineer_id: engineerId
        }
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: '工单不存在或无权操作'
        });
      }

      // 更新工单的工作总结
      await order.update({
        work_summary,
        updated_by: engineerId
      });

      // 这里可以创建一个专门的工作报告表
      // 暂时只更新工单信息

      res.json({
        success: true,
        message: '工作报告提交成功'
      });
    } catch (error) {
      console.error('提交工作报告错误:', error);
      res.status(500).json({
        success: false,
        message: '提交工作报告失败'
      });
    }
  }

  // 获取工作报告
  async getReports(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        start_date,
        end_date
      } = req.query;

      const offset = (page - 1) * limit;
      const where = {
        engineer_id: req.user.id,
        work_summary: { [Op.not]: null }
      };

      if (start_date && end_date) {
        where.completed_at = {
          [Op.between]: [new Date(start_date), new Date(end_date)]
        };
      }

      const { count, rows } = await Order.findAndCountAll({
        where,
        include: [
          { model: Equipment, as: 'equipment' },
          { model: User, as: 'reporter', attributes: ['id', 'username', 'real_name'] }
        ],
        limit: parseInt(limit),
        offset,
        order: [['completed_at', 'DESC']]
      });

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取工作报告错误:', error);
      res.status(500).json({
        success: false,
        message: '获取工作报告失败'
      });
    }
  }
}

module.exports = new EngineerController();
