const { Equipment, User, Order } = require('../models');
const { Op } = require('sequelize');
const QRCode = require('qrcode');
const path = require('path');
const fs = require('fs');

class EquipmentController {
  // 获取设备列表
  async getEquipment(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        category,
        status,
        keyword,
        user_id
      } = req.query;

      const offset = (page - 1) * limit;
      const where = {};

      // 构建查询条件
      if (category) {
        where.category = category;
      }

      if (status) {
        where.status = status;
      }

      if (keyword) {
        where[Op.or] = [
          { equipment_code: { [Op.like]: `%${keyword}%` } },
          { name: { [Op.like]: `%${keyword}%` } },
          { brand: { [Op.like]: `%${keyword}%` } },
          { model: { [Op.like]: `%${keyword}%` } }
        ];
      }

      // 如果指定了用户ID，只返回该用户的设备
      if (user_id) {
        where.user_id = user_id;
      }

      const { count, rows } = await Equipment.findAndCountAll({
        where,
        include: [
          { model: User, as: 'owner', attributes: ['id', 'username', 'real_name'] }
        ],
        limit: parseInt(limit),
        offset,
        order: [['created_at', 'DESC']]
      });

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取设备列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取设备列表失败'
      });
    }
  }

  // 获取我的设备
  async getMyEquipment(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        keyword
      } = req.query;

      const offset = (page - 1) * limit;
      const where = { user_id: req.user.id };

      if (status) {
        where.status = status;
      }

      if (keyword) {
        where[Op.or] = [
          { equipment_code: { [Op.like]: `%${keyword}%` } },
          { name: { [Op.like]: `%${keyword}%` } },
          { brand: { [Op.like]: `%${keyword}%` } },
          { model: { [Op.like]: `%${keyword}%` } }
        ];
      }

      const { count, rows } = await Equipment.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset,
        order: [['created_at', 'DESC']]
      });

      res.json({
        success: true,
        data: {
          list: rows,
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      console.error('获取我的设备错误:', error);
      res.status(500).json({
        success: false,
        message: '获取我的设备失败'
      });
    }
  }

  // 获取设备详情
  async getEquipmentById(req, res) {
    try {
      const { id } = req.params;

      const equipment = await Equipment.findByPk(id, {
        include: [
          { model: User, as: 'owner', attributes: ['id', 'username', 'real_name'] }
        ]
      });

      if (!equipment) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      // 权限检查：只有设备所有者或管理员可以查看详情
      if (req.user.role === 'user' && equipment.user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '无权访问此设备'
        });
      }

      res.json({
        success: true,
        data: equipment
      });
    } catch (error) {
      console.error('获取设备详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取设备详情失败'
      });
    }
  }

  // 创建设备
  async createEquipment(req, res) {
    try {
      const {
        name,
        category,
        brand,
        model,
        serial_number,
        purchase_date,
        warranty_period,
        location,
        description,
        specifications
      } = req.body;

      // 生成设备编号
      const equipmentCode = Equipment.generateEquipmentCode();

      // 检查序列号是否已存在
      if (serial_number) {
        const existingEquipment = await Equipment.findOne({
          where: { serial_number }
        });

        if (existingEquipment) {
          return res.status(400).json({
            success: false,
            message: '序列号已存在'
          });
        }
      }

      const equipment = await Equipment.create({
        equipment_code: equipmentCode,
        name,
        category,
        brand,
        model,
        serial_number,
        purchase_date,
        warranty_period,
        location,
        description,
        specifications,
        user_id: req.user.id,
        status: 'normal',
        created_by: req.user.id
      });

      res.status(201).json({
        success: true,
        message: '设备创建成功',
        data: equipment
      });
    } catch (error) {
      console.error('创建设备错误:', error);
      res.status(500).json({
        success: false,
        message: '创建设备失败'
      });
    }
  }

  // 更新设备
  async updateEquipment(req, res) {
    try {
      const { id } = req.params;
      const updateData = { ...req.body, updated_by: req.user.id };

      const equipment = await Equipment.findByPk(id);
      if (!equipment) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      // 权限检查：只有设备所有者或管理员可以更新
      if (req.user.role === 'user' && equipment.user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '无权修改此设备'
        });
      }

      // 检查序列号是否已被其他设备使用
      if (updateData.serial_number && updateData.serial_number !== equipment.serial_number) {
        const existingEquipment = await Equipment.findOne({
          where: {
            serial_number: updateData.serial_number,
            id: { [Op.ne]: id }
          }
        });

        if (existingEquipment) {
          return res.status(400).json({
            success: false,
            message: '序列号已被其他设备使用'
          });
        }
      }

      await equipment.update(updateData);

      res.json({
        success: true,
        message: '设备更新成功',
        data: equipment
      });
    } catch (error) {
      console.error('更新设备错误:', error);
      res.status(500).json({
        success: false,
        message: '更新设备失败'
      });
    }
  }

  // 删除设备
  async deleteEquipment(req, res) {
    try {
      const { id } = req.params;

      const equipment = await Equipment.findByPk(id);
      if (!equipment) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      // 权限检查：只有设备所有者或管理员可以删除
      if (req.user.role === 'user' && equipment.user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '无权删除此设备'
        });
      }

      // 检查是否有相关的工单
      const orderCount = await Order.count({
        where: { equipment_id: id }
      });

      if (orderCount > 0) {
        return res.status(400).json({
          success: false,
          message: '该设备有相关工单，无法删除'
        });
      }

      await equipment.destroy();

      res.json({
        success: true,
        message: '设备删除成功'
      });
    } catch (error) {
      console.error('删除设备错误:', error);
      res.status(500).json({
        success: false,
        message: '删除设备失败'
      });
    }
  }

  // 生成设备二维码
  async generateQRCode(req, res) {
    try {
      const { id } = req.params;

      const equipment = await Equipment.findByPk(id);
      if (!equipment) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      // 权限检查
      if (req.user.role === 'user' && equipment.user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '无权访问此设备'
        });
      }

      // 生成二维码内容（设备详情页面的URL或设备信息）
      const qrContent = JSON.stringify({
        type: 'equipment',
        id: equipment.id,
        code: equipment.equipment_code,
        name: equipment.name
      });

      // 生成二维码图片
      const qrCodeDataURL = await QRCode.toDataURL(qrContent, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      // 保存二维码到文件（可选）
      const fileName = `qr_${equipment.equipment_code}_${Date.now()}.png`;
      const filePath = path.join(__dirname, '../../uploads/qrcodes', fileName);
      
      // 确保目录存在
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // 将base64转换为文件
      const base64Data = qrCodeDataURL.replace(/^data:image\/png;base64,/, '');
      fs.writeFileSync(filePath, base64Data, 'base64');

      // 更新设备的二维码URL
      const qrCodeUrl = `/uploads/qrcodes/${fileName}`;
      await equipment.update({ qr_code: qrCodeUrl });

      res.json({
        success: true,
        message: '二维码生成成功',
        data: {
          qrCodeUrl: qrCodeUrl,
          qrCodeDataURL: qrCodeDataURL
        }
      });
    } catch (error) {
      console.error('生成二维码错误:', error);
      res.status(500).json({
        success: false,
        message: '生成二维码失败'
      });
    }
  }

  // 扫描二维码获取设备信息
  async scanQRCode(req, res) {
    try {
      const { qrContent } = req.body;

      let equipmentInfo;
      try {
        equipmentInfo = JSON.parse(qrContent);
      } catch (e) {
        return res.status(400).json({
          success: false,
          message: '无效的二维码内容'
        });
      }

      if (equipmentInfo.type !== 'equipment' || !equipmentInfo.id) {
        return res.status(400).json({
          success: false,
          message: '不是有效的设备二维码'
        });
      }

      const equipment = await Equipment.findByPk(equipmentInfo.id, {
        include: [
          { model: User, as: 'owner', attributes: ['id', 'username', 'real_name'] }
        ]
      });

      if (!equipment) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      res.json({
        success: true,
        data: equipment
      });
    } catch (error) {
      console.error('扫描二维码错误:', error);
      res.status(500).json({
        success: false,
        message: '扫描二维码失败'
      });
    }
  }

  // 更新设备状态
  async updateEquipmentStatus(req, res) {
    try {
      const { id } = req.params;
      const { status } = req.body;

      const equipment = await Equipment.findByPk(id);
      if (!equipment) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      await equipment.update({
        status,
        updated_by: req.user.id
      });

      res.json({
        success: true,
        message: '设备状态更新成功'
      });
    } catch (error) {
      console.error('更新设备状态错误:', error);
      res.status(500).json({
        success: false,
        message: '更新设备状态失败'
      });
    }
  }
}

module.exports = new EquipmentController();
