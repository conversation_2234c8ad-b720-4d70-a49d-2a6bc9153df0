const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

let sequelize;
let socketService;

// 安全地导入数据库和服务
try {
  const dbConfig = require('./config/database');
  sequelize = dbConfig.sequelize;
  socketService = require('./services/socketService');
} catch (error) {
  console.error('❌ 数据库配置加载失败:', error.message);
  console.log('⚠️ 应用将在没有数据库的情况下运行');
}

// 安全地导入路由
let authRoutes, userRoutes, roleRoutes, equipmentRoutes, orderRoutes, engineerRoutes, adminRoutes, chatRoutes, uploadRoutes;

try {
  authRoutes = require('./routes/auth');
  userRoutes = require('./routes/users');
  roleRoutes = require('./routes/roles');
  equipmentRoutes = require('./routes/equipment');
  orderRoutes = require('./routes/orders');
  engineerRoutes = require('./routes/engineers');
  adminRoutes = require('./routes/admin');
  chatRoutes = require('./routes/chat');
  uploadRoutes = require('./routes/upload');
} catch (error) {
  console.error('❌ 路由加载失败:', error.message);
  console.log('⚠️ 将使用简化路由');
}

const app = express();
const PORT = process.env.PORT || 80;

// 中间件配置
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-domain.com'] 
    : ['http://localhost:3000'],
  credentials: true
}));

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: '请求过于频繁，请稍后再试'
});
app.use('/api/', limiter);

app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// API路由 - 安全注册
if (authRoutes) app.use('/api/auth', authRoutes);
if (userRoutes) app.use('/api/users', userRoutes);
if (roleRoutes) app.use('/api/roles', roleRoutes);
if (equipmentRoutes) app.use('/api/equipment', equipmentRoutes);
if (orderRoutes) app.use('/api/orders', orderRoutes);
if (engineerRoutes) app.use('/api/engineers', engineerRoutes);
if (adminRoutes) app.use('/api/admin', adminRoutes);
if (chatRoutes) app.use('/api/chat', chatRoutes);
if (uploadRoutes) app.use('/api/upload', uploadRoutes);

// 如果路由加载失败，提供基本的测试路由
if (!authRoutes) {
  app.get('/api/test', (req, res) => {
    res.json({
      success: true,
      message: '基本API测试成功',
      timestamp: new Date().toISOString(),
      note: '完整路由未加载，请检查数据库连接'
    });
  });
}

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV 
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    message: '接口不存在' 
  });
});

// 全局错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: '数据验证失败',
      errors: err.details
    });
  }
  
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      message: '未授权访问'
    });
  }
  
  res.status(500).json({
    success: false,
    message: process.env.NODE_ENV === 'production' 
      ? '服务器内部错误' 
      : err.message
  });
});

// 启动服务器
const server = app.listen(PORT, '0.0.0.0', async () => {
  console.log(`🚀 服务器运行在 0.0.0.0:${PORT}`);

  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 同步数据库模型（开发环境）
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      console.log('✅ 数据库模型同步完成');
    }
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.log('⚠️ 应用将在没有数据库连接的情况下继续运行');
    console.log('📝 请在微信云托管控制台配置数据库环境变量');
  }
});

// 初始化Socket.IO
if (socketService) {
  try {
    socketService.init(server);
    console.log('✅ Socket.IO 初始化成功');
  } catch (error) {
    console.error('❌ Socket.IO 初始化失败:', error.message);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

module.exports = app;
