const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { wechatAuth } = require('../middleware/auth');

// 微信小程序登录
router.post('/wechat-login', wechatAuth, authController.wechatLogin);

// 刷新token
router.post('/refresh-token', authController.refreshToken);

// 登出
router.post('/logout', authController.logout);

// 实名认证
router.post('/verify-identity', authController.verifyIdentity);

module.exports = router;
