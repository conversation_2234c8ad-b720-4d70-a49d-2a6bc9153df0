const express = require('express');
const router = express.Router();
const roleController = require('../controllers/roleController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 所有路由都需要认证
router.use(authenticateToken);

// 获取当前用户角色信息
router.get('/my-roles', roleController.getUserRoles);

// 切换用户角色
router.post('/switch', roleController.switchRole);

// 设置默认角色
router.post('/set-default', roleController.setDefaultRole);

// 获取角色权限配置
router.get('/permissions/:role', roleController.getRolePermissions);

// 管理员功能 - 为用户添加角色
router.post('/users/:userId/roles', requireAdmin, roleController.addUserRole);

// 管理员功能 - 移除用户角色
router.delete('/users/:userId/roles/:role', requireAdmin, roleController.removeUserRole);

module.exports = router;
