const express = require('express');
const router = express.Router();
const equipmentController = require('../controllers/equipmentController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 所有路由都需要认证
router.use(authenticateToken);

// 创建设备
router.post('/', equipmentController.createEquipment);

// 批量导入设备
router.post('/batch-import', equipmentController.batchImportEquipment);

// 获取设备列表
router.get('/', equipmentController.getEquipment);

// 获取我的设备
router.get('/my', equipmentController.getMyEquipment);

// 搜索设备
router.get('/search', equipmentController.searchEquipment);

// 获取特定设备详情
router.get('/:id', equipmentController.getEquipmentById);

// 更新设备信息
router.put('/:id', equipmentController.updateEquipment);

// 删除设备（管理员）
router.delete('/:id', requireAdmin, equipmentController.deleteEquipment);

// 生成设备二维码
router.post('/:id/qrcode', equipmentController.generateQRCode);

// 扫描二维码获取设备信息
router.post('/scan', equipmentController.scanQRCode);

// 更新设备状态
router.patch('/:id/status', equipmentController.updateEquipmentStatus);

// 获取设备维修历史
router.get('/:id/history', equipmentController.getEquipmentHistory);

// 获取设备统计信息（管理员）
router.get('/stats/overview', requireAdmin, equipmentController.getEquipmentStats);

module.exports = router;
