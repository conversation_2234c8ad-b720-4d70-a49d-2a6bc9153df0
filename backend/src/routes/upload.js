const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/uploadController');
const { authenticateToken } = require('../middleware/auth');

// 所有路由都需要认证
router.use(authenticateToken);

// 上传单个图片
router.post('/image', uploadController.uploadImage);

// 上传多个图片
router.post('/images', uploadController.uploadImages);

// 上传头像
router.post('/avatar', uploadController.uploadAvatar);

// 上传文件
router.post('/file', uploadController.uploadFile);

// 获取文件信息
router.get('/info/:filename', uploadController.getFileInfo);

// 下载文件
router.get('/download/:filename', uploadController.downloadFile);

// 删除文件
router.delete('/:filename', uploadController.deleteFile);

module.exports = router;
