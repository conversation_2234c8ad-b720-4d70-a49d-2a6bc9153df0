const express = require('express');
const router = express.Router();
const chatController = require('../controllers/chatController');
const { authenticateToken } = require('../middleware/auth');

// 所有路由都需要认证
router.use(authenticateToken);

// 获取消息列表
router.get('/messages', chatController.getMessages);

// 发送消息
router.post('/send', chatController.sendMessage);

// 获取聊天列表
router.get('/list', chatController.getChatList);

// 标记消息为已读
router.patch('/read', chatController.markAsRead);

// 获取未读消息数量
router.get('/unread-count', chatController.getUnreadCount);

// 加入聊天室
router.post('/join', chatController.joinChatRoom);

// 删除消息
router.delete('/:id', chatController.deleteMessage);

module.exports = router;
