const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 所有路由都需要认证
router.use(authenticateToken);

// 获取当前用户信息
router.get('/profile', userController.getProfile);

// 更新当前用户信息
router.put('/profile', userController.updateProfile);

// 获取用户统计信息
router.get('/stats', userController.getUserStats);

// 获取认证状态
router.get('/verify-status', userController.getVerifyStatus);

// 提交实名认证
router.post('/verify', userController.submitVerification);

// 获取通知数量
router.get('/notifications/unread-count', userController.getUnreadNotificationCount);

// 获取用户列表（管理员）
router.get('/', requireAdmin, userController.getUsers);

// 获取特定用户信息（管理员）
router.get('/:id', requireAdmin, userController.getUserById);

// 更新用户信息（管理员）
router.put('/:id', requireAdmin, userController.updateUser);

// 禁用/启用用户（管理员）
router.patch('/:id/status', requireAdmin, userController.updateUserStatus);

// 分配角色（管理员）
router.patch('/:id/role', requireAdmin, userController.assignRole);

module.exports = router;
