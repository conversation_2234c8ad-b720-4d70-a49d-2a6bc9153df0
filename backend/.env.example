# 服务器配置
PORT=80
NODE_ENV=production

# 数据库配置
DB_HOST=************
DB_PORT=3306
DB_NAME=springboot_demo
DB_USER=root
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 文件上传配置
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=10485760

# 短信服务配置（可选）
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key

# 邮件服务配置（可选）
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# 云存储配置（可选）
CLOUD_STORAGE_BUCKET=your_bucket_name
CLOUD_STORAGE_REGION=your_region
CLOUD_STORAGE_ACCESS_KEY=your_access_key
CLOUD_STORAGE_SECRET_KEY=your_secret_key
