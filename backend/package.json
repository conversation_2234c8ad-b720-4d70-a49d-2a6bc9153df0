{"name": "ziguan-wei-backend", "version": "1.0.0", "description": "资管维平台后端API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "start:simple": "node src/simple-app.js", "dev": "nodemon src/app.js", "test": "jest", "migrate": "sequelize-cli db:migrate", "seed": "sequelize-cli db:seed:all", "migrate:undo": "sequelize-cli db:migrate:undo", "seed:undo": "sequelize-cli db:seed:undo:all"}, "keywords": ["equipment", "maintenance", "repair", "management", "wechat", "miniprogram"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "redis": "^4.6.11", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "axios": "^1.6.2", "moment": "^2.29.4", "joi": "^17.11.0", "qrcode": "^1.5.3", "nodemailer": "^6.9.7", "express-rate-limit": "^7.1.5", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "sequelize-cli": "^6.6.2"}, "engines": {"node": ">=16.0.0"}}