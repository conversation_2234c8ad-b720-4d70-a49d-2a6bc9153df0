# 设备二维码格式设计文档

## 概述
本文档定义了资管维平台中设备二维码的格式标准。为了解决二维码长度溢出问题，采用简化的URL格式，通过API获取完整设备信息。

## 二维码格式演进

### 当前格式（v2.0 - 简化版）
```
https://ziguanwei.example.com/miniprogram?page=pages/orders/create/create&id=EQ001&sn=CN12345678&source=qrcode
```

**设计原则：**
- 最小化二维码内容，避免长度溢出
- 通过API获取完整设备信息
- 保持向后兼容性

### 历史格式（v1.0 - 完整版）
```json
{
  "type": "equipment",
  "version": "1.0",
  "timestamp": 1703123456789,
  "data": {
    "id": "12345",
    "name": "激光打印机",
    "model": "HP LaserJet Pro M404n",
    "serialNumber": "CN12345678",
    "location": "办公室A-101",
    "status": "normal",
    "qrCode": "EQ-12345-1703123456789"
  },
  "actions": {
    "view": "/pages/equipment/detail/detail?id=12345",
    "repair": "/pages/orders/create/create?equipmentId=12345"
  }
}
```

**废弃原因：** URL过长导致二维码生成失败（1676>1056字符限制）

## 当前格式详细说明

### URL结构
```
https://ziguanwei.example.com/miniprogram?page=pages/orders/create/create&id=EQ001&sn=CN12345678&source=qrcode
```

### 参数说明
- **域名**: `ziguanwei.example.com` - 需要在微信后台配置
- **路径**: `/miniprogram` - 小程序跳转标识
- **page**: `pages/orders/create/create` - 目标页面路径
- **id**: 设备唯一标识符
- **sn**: 设备序列号（主要查询标识）
- **source**: 固定值 "qrcode"，标识来源

### 设计优势
1. **长度控制**: URL长度 < 200字符，避免二维码生成失败
2. **信息完整**: 通过API获取完整设备信息，不丢失任何数据
3. **性能优化**: 二维码生成速度更快，扫描识别更稳定
4. **维护性**: 设备信息变更不影响已生成的二维码

## API数据获取

### 接口定义
```
GET /equipment/getByIdentifier
```

### 请求参数
```json
{
  "identifier": "CN12345678",
  "type": "serialNumber"
}
```

### 响应格式
```json
{
  "success": true,
  "data": {
    "id": "EQ001",
    "name": "激光打印机",
    "model": "HP LaserJet Pro M404n",
    "serialNumber": "CN12345678",
    "location": "办公室A-101",
    "status": "normal",
    "description": "HP激光打印机，黑白打印",
    "purchaseDate": "2023-01-15",
    "warrantyExpiry": "2026-01-15",
    "brand": "HP"
  },
  "message": "设备信息获取成功"
}

## 使用场景

### 1. 设备详情查看
用户扫描二维码后，根据用户角色：
- **普通用户/PI/管理员**: 直接跳转到设备详情页面
- **工程师**: 提示无权查看设备详情，引导查看相关工单

### 2. 快速报修
通过 `actions.repair` 路径，用户可以快速为该设备创建维修工单

### 3. 设备信息验证
通过二维码中的设备信息，可以快速验证设备的基本信息

## 兼容性处理

### 旧格式支持
系统同时支持以下旧格式：
- `equipment:{设备ID}` - 简单格式
- `{纯数字}` - 设备ID

### 解析优先级
1. 首先尝试解析JSON格式
2. 如果解析失败，按旧格式处理
3. 如果都不匹配，提示无效二维码

## 生成规则

### 二维码内容
- 将JSON对象转换为字符串作为二维码内容
- 确保JSON格式正确且紧凑

### 视觉设计
- 尺寸：200x200像素
- 背景：白色 (#ffffff)
- 前景：黑色 (#000000)
- 纠错级别：H级（最高级别，30%容错率）
- 标准二维码格式，符合ISO/IEC 18004标准

### 保存格式
- 支持保存到本地相册
- 支持微信分享
- 图片格式：PNG

## 安全考虑

### 数据最小化
- 二维码中只包含必要的设备信息
- 敏感信息（如价格、供应商等）不包含在二维码中

### 时间戳验证
- 每次生成的二维码都有唯一的时间戳
- 可用于追踪二维码的生成和使用情况

### 权限控制
- 扫描后的操作受用户角色权限控制
- 工程师角色无法直接查看设备详情

## 扩展性

### 版本升级
- 通过 `version` 字段支持格式升级
- 新版本保持向后兼容

### 新增字段
- 可在 `data` 中添加新的设备信息字段
- 可在 `actions` 中添加新的操作路径

### 新增类型
- 可定义其他类型的二维码（如工单、用户等）
- 通过 `type` 字段区分不同类型

## 技术实现

### 使用的库
- **库名称**: weapp-qrcode
- **版本**: 最新版本
- **GitHub**: https://github.com/tomfriwel/weapp-qrcode
- **特点**: 专业的微信小程序二维码生成库，基于 qrcode.js

### 生成二维码
```javascript
const QRCode = require('../../../utils/weapp-qrcode.js');

// 准备数据
const qrData = {
  type: 'equipment',
  version: '1.0',
  timestamp: Date.now(),
  data: {
    id: equipment.id,
    name: equipment.name,
    model: equipment.model,
    serialNumber: equipment.serialNumber,
    location: equipment.location,
    status: equipment.status,
    qrCode: `EQ-${equipment.id}-${Date.now()}`
  },
  actions: {
    view: `/pages/equipment/detail/detail?id=${equipment.id}`,
    repair: `/pages/orders/create/create?equipmentId=${equipment.id}`
  }
};

// 生成二维码
const qrcode = new QRCode('qrCanvas', {
  usingIn: this,
  text: JSON.stringify(qrData),
  width: 200,
  height: 200,
  colorDark: '#000000',
  colorLight: '#ffffff',
  correctLevel: QRCode.CorrectLevel.H
});
```

### 解析二维码
```javascript
function parseQRCode(result) {
  try {
    const qrData = JSON.parse(result);
    if (qrData.type === 'equipment' && qrData.data) {
      return {
        type: 'equipment',
        equipmentId: qrData.data.id,
        equipmentName: qrData.data.name,
        actions: qrData.actions
      };
    }
  } catch (e) {
    // 处理旧格式或其他格式
  }
  return null;
}
```

## 测试用例

### 有效二维码测试
1. 完整JSON格式二维码
2. 最小字段JSON格式二维码
3. 旧格式兼容性测试

### 无效二维码测试
1. 格式错误的JSON
2. 缺少必要字段
3. 无效的设备ID

### 权限测试
1. 不同角色扫描相同二维码的行为
2. 权限受限时的提示信息
3. 操作路径的权限验证
