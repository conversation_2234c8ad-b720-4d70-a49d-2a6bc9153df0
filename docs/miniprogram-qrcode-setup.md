# 微信小程序二维码跳转配置指南

## 概述
本文档说明如何配置微信小程序的二维码跳转功能，使用户扫描设备二维码后能直接跳转到小程序的创建工单页面。

## 功能说明

### 二维码内容格式
生成的二维码包含以下格式的URL：
```
https://ziguanwei.example.com/miniprogram?page=pages/orders/create/create&id=设备ID&name=设备名称&model=设备型号&serialNumber=序列号&location=存放位置&status=设备状态&source=qrcode
```

### 扫描后的行为
1. **普通用户**：显示确认对话框，询问是否创建维修工单
2. **工程师用户**：直接引导创建工单（符合角色权限）
3. **自动填充**：设备信息自动填入工单表单

## 微信后台配置步骤

### 1. 登录微信公众平台
1. 访问 [微信公众平台](https://mp.weixin.qq.com/)
2. 使用小程序账号登录

### 2. 进入开发设置
1. 点击左侧菜单 "开发" → "开发管理"
2. 点击 "开发设置" 标签页
3. 找到 "扫普通链接二维码打开小程序" 模块

### 3. 开启功能
1. 点击 "开启" 按钮
2. 阅读并同意相关协议

### 4. 配置二维码规则

#### 4.1 添加二维码规则
- **二维码规则**：`https://ziguanwei.example.com/miniprogram`
- **功能页面**：`pages/orders/create/create`
- **是否占用所有子规则**：建议选择 "是"

#### 4.2 域名要求
- 域名必须通过ICP备案
- 支持 http 和 https 协议
- 建议使用 https 提高安全性

### 5. 校验文件配置

#### 5.1 下载校验文件
1. 在配置页面下载随机生成的校验文件
2. 文件名类似：`MP_verify_xxxxxxxxxx.txt`

#### 5.2 上传校验文件
将校验文件上传到服务器的指定位置：
```
https://ziguanwei.example.com/MP_verify_xxxxxxxxxx.txt
```

#### 5.3 验证访问
确保可以通过浏览器直接访问校验文件URL。

### 6. 测试配置

#### 6.1 添加测试链接
在 "测试调试" 部分添加测试链接：
```
https://ziguanwei.example.com/miniprogram?page=pages/orders/create/create&id=test001&name=测试设备&source=qrcode
```

#### 6.2 设置测试范围
- **测试版本**：选择 "开发版" 或 "体验版"
- **测试用户**：添加开发者/管理员微信号

#### 6.3 生成测试二维码
使用在线二维码生成工具生成测试链接的二维码进行测试。

### 7. 发布配置
1. 测试通过后，点击 "发布" 按钮
2. 发布后所有用户扫描符合规则的二维码都会跳转到小程序

## 域名和服务器配置

### 域名选择建议
- 使用项目专用的子域名，如：`qr.ziguanwei.com`
- 确保域名已备案且可正常访问
- 建议使用 HTTPS 协议

### 服务器配置
如果没有实际的Web服务器，可以使用以下方案：

#### 方案1：使用GitHub Pages
1. 创建GitHub仓库
2. 在仓库根目录放置校验文件
3. 启用GitHub Pages
4. 使用自定义域名

#### 方案2：使用云服务商
- 阿里云OSS + 自定义域名
- 腾讯云COS + 自定义域名
- 七牛云 + 自定义域名

#### 方案3：简单的静态服务器
```nginx
server {
    listen 80;
    server_name ziguanwei.example.com;
    
    location / {
        root /var/www/html;
        index index.html;
    }
    
    location /MP_verify_xxxxxxxxxx.txt {
        root /var/www/html;
    }
}
```

## 代码实现说明

### 二维码生成
```javascript
// 生成小程序跳转URL
generateMiniProgramUrl() {
  const equipment = this.data.equipment;
  
  const equipmentParams = {
    id: equipment.id,
    name: equipment.name,
    model: equipment.model,
    serialNumber: equipment.serialNumber,
    location: equipment.location,
    status: equipment.status,
    source: 'qrcode'
  };

  const paramString = Object.keys(equipmentParams)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(equipmentParams[key] || '')}`)
    .join('&');

  const baseUrl = 'https://ziguanwei.example.com/miniprogram';
  return `${baseUrl}?page=pages/orders/create/create&${paramString}`;
}
```

### 参数接收处理
```javascript
// 在创建工单页面的onLoad方法中
onLoad(options) {
  // 处理二维码扫描参数
  if (options.q) {
    const qrUrl = decodeURIComponent(options.q);
    const urlParams = this.parseUrlParams(qrUrl);
    
    if (urlParams.id) {
      // 自动填充设备信息
      this.setData({
        'formData.equipmentId': urlParams.id
      });
      
      // 创建设备对象并自动填充标题
      const equipment = {
        id: urlParams.id,
        name: urlParams.name,
        model: urlParams.model || '',
        // ... 其他字段
      };
      
      this.setData({ equipment });
    }
  }
}
```

## 测试验证

### 测试步骤
1. **生成二维码**：在设备详情页面生成二维码
2. **扫描测试**：使用微信扫一扫功能扫描
3. **跳转验证**：确认能正确跳转到创建工单页面
4. **数据验证**：确认设备信息正确填入表单

### 测试用例
1. **正常流程**：扫描 → 确认 → 跳转 → 自动填充
2. **取消操作**：扫描 → 取消 → 不跳转
3. **权限测试**：不同角色用户的扫描行为
4. **参数完整性**：各种设备信息的正确传递

## 注意事项

### 限制说明
- 一个小程序账号最多配置100个二维码规则
- 一个月最多发布500次二维码跳转规则
- 必须先发布小程序代码才能发布二维码规则

### 安全考虑
- 使用HTTPS协议保证传输安全
- 对URL参数进行适当的验证和过滤
- 避免在二维码中包含敏感信息

### 用户体验
- 提供清晰的扫描后确认对话框
- 自动填充信息时给予用户提示
- 处理扫描失败的错误情况

## 故障排除

### 常见问题
1. **扫描后不跳转**：检查二维码规则是否发布
2. **校验文件访问失败**：检查服务器配置和文件路径
3. **参数传递错误**：检查URL编码和解码逻辑
4. **权限问题**：确认域名备案和小程序权限

### 调试方法
1. 使用测试环境验证配置
2. 查看微信开发者工具的网络请求
3. 在代码中添加日志输出
4. 使用在线工具验证二维码内容

## 总结

通过配置微信小程序的二维码跳转功能，用户可以：
- 扫描设备二维码直接创建维修工单
- 自动填充设备信息，提高效率
- 享受流畅的移动端操作体验

这大大提升了设备维修流程的便利性和用户体验。
