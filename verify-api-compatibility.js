#!/usr/bin/env node

/**
 * API兼容性验证脚本
 * 检查小程序中使用的API接口与后端提供的接口是否匹配
 */

const fs = require('fs');
const path = require('path');

// 从小程序代码中提取API调用
function extractApiCallsFromMiniprogram() {
  const appJsPath = path.join(__dirname, 'miniprogram-user/app.js');
  const appJsContent = fs.readFileSync(appJsPath, 'utf8');
  
  // 提取所有API URL
  const urlMatches = appJsContent.match(/url\s*===?\s*['"`]([^'"`]+)['"`]/g);
  const apiCalls = new Set();
  
  if (urlMatches) {
    urlMatches.forEach(match => {
      const url = match.match(/['"`]([^'"`]+)['"`]/)[1];
      if (url.startsWith('/')) {
        apiCalls.add(url);
      }
    });
  }
  
  return Array.from(apiCalls).sort();
}

// 从后端路由中提取API端点
function extractApiEndpointsFromBackend() {
  const routesDir = path.join(__dirname, 'backend/src/routes');
  const endpoints = new Set();
  
  // 读取所有路由文件
  const routeFiles = fs.readdirSync(routesDir).filter(file => file.endsWith('.js'));
  
  routeFiles.forEach(file => {
    const filePath = path.join(routesDir, file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 提取路由定义
    const routeMatches = content.match(/router\.(get|post|put|patch|delete)\s*\(\s*['"`]([^'"`]+)['"`]/g);
    
    if (routeMatches) {
      routeMatches.forEach(match => {
        const parts = match.match(/router\.(\w+)\s*\(\s*['"`]([^'"`]+)['"`]/);
        if (parts) {
          const method = parts[1].toUpperCase();
          const path = parts[2];
          const routePrefix = file.replace('.js', '');
          endpoints.add(`${method} /${routePrefix}${path}`);
        }
      });
    }
  });
  
  return Array.from(endpoints).sort();
}

// 检查API兼容性
function checkApiCompatibility() {
  console.log('🔍 检查API兼容性...\n');
  
  const miniprogramApis = extractApiCallsFromMiniprogram();
  const backendEndpoints = extractApiEndpointsFromBackend();
  
  console.log('📱 小程序中使用的API接口:');
  miniprogramApis.forEach(api => {
    console.log(`  ${api}`);
  });
  
  console.log('\n🔧 后端提供的API端点:');
  backendEndpoints.forEach(endpoint => {
    console.log(`  ${endpoint}`);
  });
  
  console.log('\n🔍 兼容性检查结果:');
  
  // 检查小程序API是否在后端有对应实现
  const missingEndpoints = [];
  
  miniprogramApis.forEach(api => {
    // 简化匹配逻辑，检查路径是否存在对应的后端端点
    const found = backendEndpoints.some(endpoint => {
      const endpointPath = endpoint.split(' ')[1];
      return endpointPath.includes(api.replace(/^\//, ''));
    });
    
    if (!found) {
      missingEndpoints.push(api);
    }
  });
  
  if (missingEndpoints.length === 0) {
    console.log('✅ 所有API接口都有对应的后端实现');
  } else {
    console.log('❌ 以下API接口缺少后端实现:');
    missingEndpoints.forEach(api => {
      console.log(`  ${api}`);
    });
  }
  
  // 特别检查关键API
  console.log('\n🔑 关键API检查:');
  
  const criticalApis = [
    '/auth/wechat-login',
    '/equipment/getByIdentifier',
    '/orders',
    '/chat/messages',
    '/users/profile'
  ];
  
  criticalApis.forEach(api => {
    const inMiniprogram = miniprogramApis.includes(api);
    const hasBackendSupport = backendEndpoints.some(endpoint => 
      endpoint.includes(api.replace(/^\//, ''))
    );
    
    const status = inMiniprogram && hasBackendSupport ? '✅' : 
                   inMiniprogram && !hasBackendSupport ? '❌' : 
                   !inMiniprogram && hasBackendSupport ? '⚠️' : '⭕';
    
    console.log(`  ${status} ${api}`);
  });
  
  console.log('\n📝 说明:');
  console.log('  ✅ - API在小程序中使用且后端已实现');
  console.log('  ❌ - API在小程序中使用但后端未实现');
  console.log('  ⚠️ - 后端已实现但小程序未使用');
  console.log('  ⭕ - API未使用且未实现');
  
  return missingEndpoints.length === 0;
}

// 检查数据库配置
function checkDatabaseConfig() {
  console.log('\n🗄️ 检查数据库配置...');
  
  const sqlFiles = [
    'mysql_schema.sql',
    'backend/database/init-cloud.sql'
  ];
  
  sqlFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file} 存在`);
    } else {
      console.log(`❌ ${file} 不存在`);
    }
  });
}

// 检查部署文件
function checkDeploymentFiles() {
  console.log('\n🚀 检查部署文件...');
  
  const deploymentFiles = [
    'backend/Dockerfile',
    'backend/.dockerignore',
    'backend/.env.example',
    'deploy.sh',
    'WXCLOUD_DEPLOYMENT.md'
  ];
  
  deploymentFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file} 存在`);
    } else {
      console.log(`❌ ${file} 不存在`);
    }
  });
}

// 主函数
function main() {
  console.log('🔧 资管维平台 - API兼容性验证\n');
  
  const isCompatible = checkApiCompatibility();
  checkDatabaseConfig();
  checkDeploymentFiles();
  
  console.log('\n📋 部署前检查清单:');
  console.log('  1. ✅ API兼容性已验证');
  console.log('  2. ✅ 数据库脚本已准备');
  console.log('  3. ✅ Docker配置已完成');
  console.log('  4. ✅ 部署脚本已准备');
  
  if (isCompatible) {
    console.log('\n🎉 所有检查通过，可以开始部署！');
    console.log('\n下一步操作:');
    console.log('  1. 安装微信云托管CLI: npm install -g @wxcloud/cli');
    console.log('  2. 设置环境变量 (参考 WXCLOUD_DEPLOYMENT.md)');
    console.log('  3. 运行部署脚本: ./deploy.sh');
  } else {
    console.log('\n⚠️ 发现兼容性问题，请先修复后再部署');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  extractApiCallsFromMiniprogram,
  extractApiEndpointsFromBackend,
  checkApiCompatibility
};
