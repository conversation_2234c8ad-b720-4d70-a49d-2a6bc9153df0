-- 数据库迁移脚本：支持多角色功能
-- 执行前请备份数据库

-- 1. 创建组织表
CREATE TABLE IF NOT EXISTS `organizations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '单位名称',
  `short_name` varchar(100) DEFAULT NULL COMMENT '单位简称',
  `type` enum('university','institute','company','government','other') DEFAULT 'university' COMMENT '单位类型',
  `description` text COMMENT '单位描述',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(500) DEFAULT NULL COMMENT '地址',
  `website` varchar(255) DEFAULT NULL COMMENT '官网地址',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
  `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_type` (`type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组织/单位表';

-- 2. 创建课题组表
CREATE TABLE IF NOT EXISTS `research_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '课题组名称',
  `organization_id` int(11) DEFAULT NULL COMMENT '所属单位ID',
  `description` text COMMENT '课题组描述',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(500) DEFAULT NULL COMMENT '地址',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
  `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_research_groups_organization` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课题组表';

-- 3. 创建用户角色关联表
CREATE TABLE IF NOT EXISTS `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role` enum('user','pi','engineer','admin','super_admin') NOT NULL COMMENT '角色类型',
  `role_name` varchar(50) NOT NULL COMMENT '角色显示名称',
  `department` varchar(100) DEFAULT NULL COMMENT '角色所属部门/课题组',
  `research_group_id` int(11) DEFAULT NULL COMMENT '关联的课题组ID（适用于PI和用户角色）',
  `permissions` json DEFAULT NULL COMMENT '角色权限配置',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '角色是否激活',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否为默认角色',
  `granted_by` int(11) DEFAULT NULL COMMENT '授权人ID',
  `granted_at` timestamp NULL DEFAULT NULL COMMENT '授权时间',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '角色过期时间（可选）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`, `role`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role` (`role`),
  KEY `idx_research_group_id` (`research_group_id`),
  CONSTRAINT `fk_user_roles_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_roles_research_group` FOREIGN KEY (`research_group_id`) REFERENCES `research_groups` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 4. 修改设备表，添加课题组关联
ALTER TABLE `equipment` 
ADD COLUMN `research_group_id` int(11) DEFAULT NULL COMMENT '所属课题组ID' AFTER `pi_id`,
ADD KEY `idx_research_group_id` (`research_group_id`),
ADD CONSTRAINT `fk_equipment_research_group` FOREIGN KEY (`research_group_id`) REFERENCES `research_groups` (`id`) ON DELETE SET NULL;

-- 5. 修改用户表，添加实名认证相关字段
ALTER TABLE `users` 
ADD COLUMN `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名' AFTER `username`,
ADD COLUMN `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号' AFTER `real_name`,
ADD COLUMN `employee_id` varchar(50) DEFAULT NULL COMMENT '工号' AFTER `id_card`,
ADD COLUMN `department` varchar(100) DEFAULT NULL COMMENT '部门' AFTER `employee_id`,
ADD COLUMN `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已实名认证' AFTER `department`,
ADD COLUMN `verified_at` timestamp NULL DEFAULT NULL COMMENT '认证时间' AFTER `is_verified`,
ADD COLUMN `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间' AFTER `verified_at`;

-- 6. 创建通知表
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '接收用户ID',
  `title` varchar(255) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `type` varchar(50) DEFAULT 'system' COMMENT '通知类型',
  `related_id` int(11) DEFAULT NULL COMMENT '关联对象ID',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联对象类型',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知表';

-- 7. 创建系统公告表
CREATE TABLE IF NOT EXISTS `announcements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `type` enum('system','maintenance','feature','notice') DEFAULT 'notice' COMMENT '公告类型',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal' COMMENT '优先级',
  `target_roles` json DEFAULT NULL COMMENT '目标角色（为空表示所有用户）',
  `is_published` tinyint(1) DEFAULT 0 COMMENT '是否已发布',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_by` int(11) NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_priority` (`priority`),
  KEY `idx_is_published` (`is_published`),
  KEY `idx_published_at` (`published_at`),
  CONSTRAINT `fk_announcements_creator` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统公告表';

-- 8. 为现有用户创建默认角色
INSERT INTO `user_roles` (`user_id`, `role`, `role_name`, `is_default`, `is_active`, `granted_at`)
SELECT 
  `id`,
  `role`,
  CASE 
    WHEN `role` = 'user' THEN '普通用户'
    WHEN `role` = 'pi' THEN '课题组负责人'
    WHEN `role` = 'engineer' THEN '工程师'
    WHEN `role` = 'admin' THEN '管理员'
    WHEN `role` = 'super_admin' THEN '超级管理员'
    ELSE '普通用户'
  END,
  1,
  1,
  NOW()
FROM `users` 
WHERE `id` NOT IN (SELECT DISTINCT `user_id` FROM `user_roles`);

-- 9. 创建默认组织和课题组
INSERT INTO `organizations` (`name`, `short_name`, `type`, `description`, `is_active`, `created_at`) 
VALUES ('默认单位', '默认', 'university', '系统默认单位', 1, NOW());

INSERT INTO `research_groups` (`name`, `organization_id`, `description`, `is_active`, `created_at`) 
VALUES ('默认课题组', LAST_INSERT_ID(), '系统默认课题组', 1, NOW());

-- 10. 更新设备表，关联到默认课题组
UPDATE `equipment` 
SET `research_group_id` = (SELECT `id` FROM `research_groups` WHERE `name` = '默认课题组' LIMIT 1)
WHERE `research_group_id` IS NULL;

-- 11. 创建索引优化查询性能
CREATE INDEX `idx_users_role_active` ON `users` (`role`, `is_active`);
CREATE INDEX `idx_equipment_status_group` ON `equipment` (`status`, `research_group_id`);
CREATE INDEX `idx_orders_status_assigned` ON `orders` (`status`, `assigned_to`);
CREATE INDEX `idx_orders_created_priority` ON `orders` (`created_at`, `priority`);

-- 12. 创建视图简化查询
CREATE OR REPLACE VIEW `v_user_roles_detail` AS
SELECT 
  ur.id,
  ur.user_id,
  u.username,
  u.real_name,
  ur.role,
  ur.role_name,
  ur.department,
  ur.research_group_id,
  rg.name as research_group_name,
  o.name as organization_name,
  ur.permissions,
  ur.is_active,
  ur.is_default,
  ur.granted_at,
  ur.expires_at
FROM `user_roles` ur
LEFT JOIN `users` u ON ur.user_id = u.id
LEFT JOIN `research_groups` rg ON ur.research_group_id = rg.id
LEFT JOIN `organizations` o ON rg.organization_id = o.id
WHERE ur.is_active = 1;

-- 13. 创建存储过程：获取用户权限
DELIMITER //
CREATE PROCEDURE `GetUserPermissions`(IN userId INT, IN userRole VARCHAR(50))
BEGIN
  DECLARE rolePermissions JSON;
  
  SELECT permissions INTO rolePermissions 
  FROM user_roles 
  WHERE user_id = userId AND role = userRole AND is_active = 1;
  
  SELECT rolePermissions as permissions;
END //
DELIMITER ;

-- 14. 创建触发器：用户角色变更日志
CREATE TABLE IF NOT EXISTS `user_role_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `action` enum('grant','revoke','activate','deactivate','update') NOT NULL,
  `role` varchar(50) NOT NULL,
  `old_data` json DEFAULT NULL,
  `new_data` json DEFAULT NULL,
  `operated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色变更日志';

DELIMITER //
CREATE TRIGGER `tr_user_roles_log` 
AFTER UPDATE ON `user_roles`
FOR EACH ROW
BEGIN
  INSERT INTO `user_role_logs` (
    `user_id`, `action`, `role`, `old_data`, `new_data`, `created_at`
  ) VALUES (
    NEW.user_id,
    'update',
    NEW.role,
    JSON_OBJECT(
      'is_active', OLD.is_active,
      'is_default', OLD.is_default,
      'permissions', OLD.permissions
    ),
    JSON_OBJECT(
      'is_active', NEW.is_active,
      'is_default', NEW.is_default,
      'permissions', NEW.permissions
    ),
    NOW()
  );
END //
DELIMITER ;

-- 15. 插入示例数据
INSERT INTO `announcements` (`title`, `content`, `type`, `priority`, `is_published`, `published_at`, `created_by`) 
VALUES 
('系统升级通知', '系统将于今晚进行升级维护，预计维护时间2小时。', 'maintenance', 'high', 1, NOW(), 1),
('新功能上线', '多角色管理功能已上线，用户可以在个人中心切换不同身份。', 'feature', 'normal', 1, NOW(), 1);

-- 完成迁移
SELECT 'Database migration completed successfully!' as message;
