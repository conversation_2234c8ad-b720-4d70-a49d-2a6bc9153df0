-- 2.1 组织/单位表 (Organizations) -- 课题组的上一级单位，如“XX大学”
use springboot_demo;

CREATE TABLE organizations (
                               id INT PRIMARY KEY AUTO_INCREMENT,
                               name VARCHAR(255) NOT NULL UNIQUE COMMENT '单位名称'
);


-- 2.2 课题组表 (Research Groups)
CREATE TABLE research_groups (
                                 id INT PRIMARY KEY AUTO_INCREMENT,
                                 name VARCHAR(255) NOT NULL COMMENT '课题组名称',
                                 organization_id INT COMMENT '所属单位',
                                 pi_user_id INT COMMENT '该组的PI负责人 (关联用户表)',
                                 FOREIGN KEY (organization_id) REFERENCES organizations(id)
);

-- 2.3 设备品牌库 (Equipment Brands)
CREATE TABLE equipment_brands (
                                  id INT PRIMARY KEY AUTO_INCREMENT,
                                  name VARCHAR(100) NOT NULL UNIQUE COMMENT '品牌名称'
);

-- 2.4 设备型号库 (Equipment Models)
CREATE TABLE equipment_models (
                                  id INT PRIMARY KEY AUTO_INCREMENT,
                                  brand_id INT NOT NULL,
                                  name VARCHAR(100) NOT NULL COMMENT '型号名称',
                                  UNIQUE (brand_id, name),
                                  FOREIGN KEY (brand_id) REFERENCES equipment_brands(id)
);


-- 2.5 设备档案表 (Equipment)
CREATE TABLE equipment (
                           id INT PRIMARY KEY AUTO_INCREMENT,
                           name VARCHAR(255) NOT NULL COMMENT '设备自定义名称',
                           maintenance_code VARCHAR(100) NOT NULL UNIQUE COMMENT '维护编号 (唯一)',
                           qr_code_url VARCHAR(255) COMMENT '二维码图片URL',
                           research_group_id INT NOT NULL COMMENT '所属课题组',
                           model_id INT COMMENT '外键, 关联标准型号库',
                           serial_number VARCHAR(255) COMMENT '序列号',
                           purchase_date DATE COMMENT '启用/购买日期',
                           is_under_warranty BOOLEAN COMMENT '是否过保',
                           custom_fields JSON COMMENT '预留自定义字段 (JSON格式)',
                           created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                           updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                           deleted_at TIMESTAMP NULL COMMENT '软删除标记',
                           FOREIGN KEY (research_group_id) REFERENCES research_groups(id),
                           FOREIGN KEY (model_id) REFERENCES equipment_models(id)
);



-- 1.1 角色表 (Roles)
CREATE TABLE roles (
                       id INT PRIMARY KEY AUTO_INCREMENT,
                       role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称 (SuperAdmin, SubAdmin, PI, User, Engineer)'
);

-- 1.2 用户表 (Users) -- 存储所有三端用户的核心信息
CREATE TABLE users (
                       id INT PRIMARY KEY AUTO_INCREMENT,
                       username VARCHAR(100) NOT NULL UNIQUE COMMENT '登录用户名',
                       password_hash VARCHAR(255) NOT NULL COMMENT '加密后的密码',
                       full_name VARCHAR(100) NOT NULL COMMENT '用户真实姓名 (实名制)',
                       phone_number VARCHAR(20) UNIQUE COMMENT '手机号',
                       email VARCHAR(100) UNIQUE COMMENT '邮箱',
                       role_id INT NOT NULL COMMENT '外键, 关联角色',
                       research_group_id INT COMMENT '外键, 关联所属课题组 (客户用户)',
                       is_active BOOLEAN DEFAULT true COMMENT '账户是否激活/禁用',
                       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                       deleted_at TIMESTAMP NULL COMMENT '软删除标记',
                       FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- 2. 创建完所有表后，再添加外键约束
ALTER TABLE research_groups
    ADD CONSTRAINT fk_rg_pi_user
        FOREIGN KEY (pi_user_id) REFERENCES users(id);

ALTER TABLE users
    ADD CONSTRAINT fk_users_rg
        FOREIGN KEY (research_group_id) REFERENCES research_groups(id);

-- 1.3 权限细节 (可预留，用于更细粒度的控制，例如子管理员)
CREATE TABLE user_permissions (
                                  id INT PRIMARY KEY AUTO_INCREMENT,
                                  user_id INT NOT NULL COMMENT '被授权的用户 (特指子管理员)',
                                  managed_group_id INT COMMENT '被授权管理的课题组ID',
    -- can_edit_users BOOLEAN DEFAULT false,
    -- can_dispatch_orders BOOLEAN DEFAULT false,
    -- ... 其他细分权限
                                  FOREIGN KEY (user_id) REFERENCES users(id),
                                  FOREIGN KEY (managed_group_id) REFERENCES research_groups(id)
);

-- 3.1 工单表 (Work Orders)
CREATE TABLE work_orders (
                             id INT PRIMARY KEY AUTO_INCREMENT,
                             order_number VARCHAR(50) NOT NULL UNIQUE COMMENT '工单号 (系统生成)',
                             equipment_id INT NOT NULL COMMENT '报修的设备',
                             created_by_user_id INT NOT NULL COMMENT '报修人',
                             assigned_to_engineer_id INT COMMENT '被指派的工程师',
                             fault_description TEXT NOT NULL COMMENT '故障现象描述',
                             status ENUM(
                                 'Pending Confirmation', -- 待处理/待确认
                                 'Pending Dispatch',     -- 待派单 (猜测补充)
                                 'Pending Service',      -- 待维修 (已派单)
                                 'In Progress',          -- 维修中
                                 'Pending Parts',        -- 待配件
                                 'Pending Acceptance',   -- 待验收 (维修完成)
                                 'Pending Billing',      -- 待结算
                                 'Billing In Progress',  -- 结算中
                                 'Completed',            -- 已完成
                                 'Cancelled'             -- 已取消
                                 ) NOT NULL DEFAULT 'Pending Confirmation',
                             is_urgent BOOLEAN DEFAULT false COMMENT '是否为紧急工单',
                             repair_report TEXT COMMENT '维修报告内容',
                             customer_signature_url VARCHAR(255) COMMENT '客户电子签名图片URL',
                             created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                             updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                             completed_at TIMESTAMP NULL COMMENT '工单完成时间',
                             FOREIGN KEY (equipment_id) REFERENCES equipment(id),
                             FOREIGN KEY (created_by_user_id) REFERENCES users(id),
                             FOREIGN KEY (assigned_to_engineer_id) REFERENCES users(id)
);

-- 3.2 工单更新日志 (Work Order Updates) -- 追踪所有状态变更和沟通记录
CREATE TABLE work_order_updates (
                                    id INT PRIMARY KEY AUTO_INCREMENT,
                                    work_order_id INT NOT NULL,
                                    user_id INT COMMENT '操作人',
                                    update_type VARCHAR(50) NOT NULL COMMENT '更新类型 (StatusChange, Comment)',
                                    previous_status VARCHAR(50),
                                    new_status VARCHAR(50),
                                    comment TEXT,
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    FOREIGN KEY (work_order_id) REFERENCES work_orders(id),
                                    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 3.3 服务评价表 (Service Evaluations)
CREATE TABLE service_evaluations (
                                     id INT PRIMARY KEY AUTO_INCREMENT,
                                     work_order_id INT NOT NULL UNIQUE,
                                     rating INT NOT NULL COMMENT '评分 (例如 1-4)',
                                     feedback TEXT,
                                     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                     FOREIGN KEY (work_order_id) REFERENCES work_orders(id)
);

-- 4.1 备件/配件表 (Parts)
CREATE TABLE parts (
                       id INT PRIMARY KEY AUTO_INCREMENT,
                       part_number VARCHAR(100) UNIQUE COMMENT '配件料号',
                       name VARCHAR(255) NOT NULL,
                       description TEXT,
                       stock_quantity INT NOT NULL DEFAULT 0,
                       unit_price DECIMAL(10, 2)
);

-- 4.2 工单使用配件记录 (Work Order Parts Usage)
CREATE TABLE work_order_parts (
                                  id INT PRIMARY KEY AUTO_INCREMENT,
                                  work_order_id INT NOT NULL,
                                  part_id INT NOT NULL,
                                  quantity_used INT NOT NULL,
                                  logged_by_engineer_id INT NOT NULL,
                                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                  FOREIGN KEY (work_order_id) REFERENCES work_orders(id),
                                  FOREIGN KEY (part_id) REFERENCES parts(id),
                                  FOREIGN KEY (logged_by_engineer_id) REFERENCES users(id)
);

-- 4.3 配件采购申请 (Part Requests)
CREATE TABLE part_requests (
                               id INT PRIMARY KEY AUTO_INCREMENT,
                               part_id INT NOT NULL,
                               quantity_requested INT NOT NULL,
                               requested_by_engineer_id INT NOT NULL,
                               status ENUM('Pending', 'Approved', 'Rejected', 'Ordered') NOT NULL DEFAULT 'Pending',
                               approved_by_admin_id INT,
                               created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                               FOREIGN KEY (part_id) REFERENCES parts(id),
                               FOREIGN KEY (requested_by_engineer_id) REFERENCES users(id),
                               FOREIGN KEY (approved_by_admin_id) REFERENCES users(id)
);

-- 5.1 附件表 (Attachments) -- 通用附件表，可关联到任何其他表
CREATE TABLE attachments (
                             id INT PRIMARY KEY AUTO_INCREMENT,
                             file_name VARCHAR(255) NOT NULL,
                             file_path VARCHAR(255) NOT NULL,
                             file_type VARCHAR(50),
                             attachable_id INT NOT NULL COMMENT '关联对象的ID',
                             attachable_type VARCHAR(50) NOT NULL COMMENT '关联对象的类型 (e.g., Equipment, WorkOrder)',
                             uploaded_by_user_id INT,
                             created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                             FOREIGN KEY (uploaded_by_user_id) REFERENCES users(id)
);

-- 5.2 实时聊天消息 (Chat Messages)
CREATE TABLE chat_messages (
                               id INT PRIMARY KEY AUTO_INCREMENT,
                               work_order_id INT NOT NULL,
                               sender_id INT NOT NULL,
                               receiver_id INT NOT NULL,
                               message_type ENUM('text', 'voice', 'image') NOT NULL DEFAULT 'text',
                               content TEXT NOT NULL,
                               sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                               FOREIGN KEY (work_order_id) REFERENCES work_orders(id),
                               FOREIGN KEY (sender_id) REFERENCES users(id),
                               FOREIGN KEY (receiver_id) REFERENCES users(id)
);

-- 5.3 系统公告 (System Announcements)
CREATE TABLE system_announcements (
                                      id INT PRIMARY KEY AUTO_INCREMENT,
                                      title VARCHAR(255) NOT NULL,
                                      content TEXT NOT NULL,
                                      created_by_admin_id INT NOT NULL,
                                      is_published BOOLEAN DEFAULT false,
                                      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                      FOREIGN KEY (created_by_admin_id) REFERENCES users(id)
);

