# 微信小程序二维码跳转功能测试指南

## 测试目标
验证设备二维码生成微信小程序跳转链接，用户扫描后能直接跳转到创建工单页面并自动填入设备信息。

## 功能概述

### 二维码内容格式
现在生成的二维码包含微信小程序跳转链接：
```
https://ziguanwei.example.com/miniprogram?page=pages/orders/create/create&id=设备ID&name=设备名称&model=设备型号&serialNumber=序列号&location=存放位置&status=设备状态&source=qrcode
```

### 扫描后的流程
1. **微信扫一扫** → 识别为小程序跳转链接
2. **显示确认对话框** → "检测到设备维修二维码，是否要创建维修工单？"
3. **用户确认** → 跳转到小程序创建工单页面
4. **自动填充** → 设备信息自动填入表单

## 前置条件

### 微信后台配置
在开始测试前，需要在微信公众平台完成以下配置：

1. **开启功能**：扫普通链接二维码打开小程序
2. **配置规则**：`https://ziguanwei.example.com/miniprogram`
3. **功能页面**：`pages/orders/create/create`
4. **校验文件**：上传并验证域名所有权
5. **发布规则**：将配置发布到线上环境

详细配置步骤请参考：`docs/miniprogram-qrcode-setup.md`

## 测试步骤

### 1. 二维码生成测试
1. 进入任意设备详情页面
2. 点击"生成二维码"按钮
3. 观察生成的二维码

**预期结果：**
- ✅ 显示"生成中..."加载提示
- ✅ 生成清晰的二维码图片
- ✅ 二维码包含小程序跳转链接
- ✅ 控制台输出正确的URL格式

### 2. 二维码内容验证
1. 使用二维码扫描工具扫描生成的二维码
2. 查看扫描结果的URL内容
3. 验证URL参数是否完整

**预期URL格式：**
```
https://ziguanwei.example.com/miniprogram?page=pages/orders/create/create&id=EQ001&name=%E6%BF%80%E5%85%89%E6%89%93%E5%8D%B0%E6%9C%BA&model=HP%20LaserJet&serialNumber=SN123456&location=%E5%8A%9E%E5%85%AC%E5%AE%A4A101&status=normal&source=qrcode
```

**验证要点：**
- ✅ URL包含正确的域名和路径
- ✅ page参数指向创建工单页面
- ✅ 设备信息参数完整且正确编码
- ✅ source参数标识为qrcode来源

### 3. 微信扫码跳转测试

#### 3.1 基本跳转测试
1. 使用微信"扫一扫"功能扫描二维码
2. 观察微信的识别和处理过程
3. 验证是否正确跳转到小程序

**预期结果：**
- ✅ 微信识别为小程序跳转链接
- ✅ 显示小程序信息和跳转确认
- ✅ 点击确认后跳转到小程序
- ✅ 直接进入创建工单页面

#### 3.2 参数传递测试
1. 扫码跳转到创建工单页面后
2. 检查设备信息是否正确填入表单
3. 验证自动填充的内容

**预期结果：**
- ✅ 设备ID正确设置
- ✅ 设备信息显示在页面上
- ✅ 工单标题自动生成（如："激光打印机 - HP LaserJet 维修申请"）
- ✅ 显示"已自动填入设备信息"提示

### 4. 不同角色测试

#### 4.1 普通用户测试
1. 使用普通用户身份扫描二维码
2. 验证跳转和填充功能

**预期结果：**
- ✅ 正常跳转到创建工单页面
- ✅ 设备信息正确填充
- ✅ 可以正常提交工单

#### 4.2 工程师用户测试
1. 使用工程师身份扫描二维码
2. 验证是否有特殊处理

**预期结果：**
- ✅ 正常跳转到创建工单页面
- ✅ 设备信息正确填充
- ✅ 符合工程师角色的权限设置

### 5. 小程序内扫码测试

#### 5.1 首页扫码功能
1. 在小程序首页点击扫码功能
2. 扫描设备二维码
3. 验证处理逻辑

**预期结果：**
- ✅ 识别为小程序跳转链接
- ✅ 显示确认对话框："检测到设备维修二维码，是否要创建维修工单？"
- ✅ 确认后跳转到创建工单页面
- ✅ 设备信息正确传递

#### 5.2 参数解析测试
1. 验证URL参数的正确解析
2. 检查中文字符的编码解码
3. 验证特殊字符的处理

**预期结果：**
- ✅ 中文设备名称正确显示
- ✅ 特殊字符不会导致解析错误
- ✅ 空值参数正确处理

### 6. 错误处理测试

#### 6.1 无效二维码测试
1. 扫描格式错误的二维码
2. 扫描不完整的URL
3. 验证错误处理

**预期结果：**
- ✅ 显示"二维码格式错误"提示
- ✅ 不会导致程序崩溃
- ✅ 用户可以重新扫描

#### 6.2 网络异常测试
1. 在网络不稳定环境下测试
2. 验证降级处理

**预期结果：**
- ✅ 网络异常时有合适的提示
- ✅ 不影响基本的跳转功能

### 7. 兼容性测试

#### 7.1 微信版本兼容性
- 最新版本微信（推荐）
- 微信 6.5.6 及以上版本
- iOS 和 Android 平台

#### 7.2 设备兼容性
- iPhone 设备测试
- Android 设备测试
- 不同屏幕尺寸测试

## 性能测试

### 响应速度
- ✅ 二维码生成时间 < 2秒
- ✅ 扫码识别响应时间 < 1秒
- ✅ 页面跳转时间 < 3秒
- ✅ 参数解析和填充时间 < 1秒

### 用户体验
- ✅ 操作流程直观简单
- ✅ 提示信息清晰友好
- ✅ 错误处理合理
- ✅ 自动填充提升效率

## 边界情况测试

### 数据边界
1. **超长设备名称**：测试很长的设备名称
2. **特殊字符**：测试包含特殊符号的设备信息
3. **空值处理**：测试某些字段为空的情况
4. **URL长度**：验证URL总长度是否在合理范围

### 用户行为
1. **取消操作**：扫码后点击取消
2. **重复扫码**：多次扫描同一个二维码
3. **快速操作**：快速连续扫码

## 故障排除

### 常见问题
1. **扫码后不跳转**：
   - 检查微信后台配置是否正确
   - 验证二维码规则是否已发布
   - 确认域名和校验文件配置

2. **参数传递错误**：
   - 检查URL编码是否正确
   - 验证参数解析逻辑
   - 查看控制台日志

3. **页面显示异常**：
   - 检查设备信息格式
   - 验证表单字段映射
   - 确认页面权限设置

### 调试方法
1. **查看控制台日志**：
   ```javascript
   console.log('二维码扫描URL:', qrUrl);
   console.log('解析的URL参数:', urlParams);
   ```

2. **验证URL格式**：
   使用在线URL解码工具验证参数

3. **测试环境验证**：
   在微信开发者工具中测试跳转逻辑

## 总结

### ✅ 功能优势
1. **便利性**：扫码即可创建工单，无需手动输入设备信息
2. **准确性**：自动填充避免输入错误
3. **效率性**：大大减少用户操作步骤
4. **专业性**：符合微信小程序的标准跳转机制

### 🎯 用户价值
1. **维修人员**：快速为设备创建维修工单
2. **设备管理员**：便捷的设备维修流程
3. **普通用户**：简化的报修操作体验

### 📈 业务价值
1. **提升效率**：减少手动输入时间
2. **降低错误**：自动填充减少人为错误
3. **改善体验**：流畅的移动端操作
4. **数据准确**：确保设备信息的一致性

这个功能将设备维修流程数字化，大大提升了用户体验和工作效率！
