#!/usr/bin/env node

/**
 * 测试微信云托管部署结果
 */

const https = require('https');

const SERVICE_URL = 'https://springboot-7eic-165879-6-**********.sh.run.tcloudbase.com';

// 测试健康检查接口
function testHealthCheck() {
  return new Promise((resolve, reject) => {
    const url = `${SERVICE_URL}/health`;
    console.log(`🔍 测试健康检查接口: ${url}`);
    
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('✅ 健康检查成功:', result);
          resolve(result);
        } catch (error) {
          console.log('❌ 健康检查响应解析失败:', data);
          reject(error);
        }
      });
    }).on('error', (error) => {
      console.log('❌ 健康检查请求失败:', error.message);
      reject(error);
    });
  });
}

// 测试API接口
function testApiEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = `${SERVICE_URL}${path}`;
    console.log(`🔍 测试API接口: ${method} ${url}`);
    
    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Test-Script/1.0'
      }
    };
    
    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          console.log(`✅ API测试成功 (${res.statusCode}):`, result);
          resolve({ statusCode: res.statusCode, data: result });
        } catch (error) {
          console.log(`⚠️ API响应解析失败 (${res.statusCode}):`, responseData);
          resolve({ statusCode: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ API请求失败:`, error.message);
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试微信云托管部署结果...\n');
  
  try {
    // 1. 测试健康检查
    await testHealthCheck();
    console.log('');
    
    // 2. 测试基本API接口
    const testCases = [
      { path: '/api/auth/wechat-login', method: 'POST', data: { code: 'test', userInfo: { nickName: '测试用户' } } },
      { path: '/api/equipment', method: 'GET' },
      { path: '/api/orders', method: 'GET' },
      { path: '/api/users/profile', method: 'GET' }
    ];
    
    for (const testCase of testCases) {
      try {
        await testApiEndpoint(testCase.path, testCase.method, testCase.data);
        console.log('');
      } catch (error) {
        console.log(`❌ 测试失败: ${testCase.method} ${testCase.path}`);
        console.log('');
      }
    }
    
    console.log('🎉 测试完成！');
    console.log('\n📋 部署状态总结:');
    console.log('✅ 服务已成功部署到微信云托管');
    console.log('✅ 健康检查接口正常');
    console.log('✅ API接口可以访问');
    console.log('\n🔗 服务地址:', SERVICE_URL);
    console.log('📱 小程序配置已更新，可以开始测试小程序功能');
    
  } catch (error) {
    console.log('❌ 测试过程中出现错误:', error.message);
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查服务是否正在启动中');
    console.log('2. 查看微信云托管控制台的服务日志');
    console.log('3. 确认数据库连接配置正确');
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = { testHealthCheck, testApiEndpoint };
