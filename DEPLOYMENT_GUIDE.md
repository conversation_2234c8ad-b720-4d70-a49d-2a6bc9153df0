# 资管维平台部署指南

## 概述

本文档描述了资管维平台的完整部署流程，包括后端API服务、数据库配置、微信小程序部署等。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户端小程序   │    │   工程师端小程序 │    │   管理后台      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Backend API   │
                    │   (Node.js)     │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL DB      │
                    └─────────────────┘
```

## 环境要求

### 服务器环境
- **操作系统**: Ubuntu 20.04 LTS 或 CentOS 8+
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 50GB以上
- **网络**: 公网IP，支持HTTPS

### 软件依赖
- **Node.js**: v16.x 或更高版本
- **MySQL**: v8.0 或更高版本
- **Nginx**: v1.18 或更高版本
- **PM2**: 进程管理器
- **SSL证书**: 用于HTTPS

## 部署步骤

### 1. 服务器准备

#### 1.1 更新系统
```bash
# Ubuntu
sudo apt update && sudo apt upgrade -y

# CentOS
sudo yum update -y
```

#### 1.2 安装Node.js
```bash
# 使用NodeSource仓库安装Node.js 16.x
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### 1.3 安装MySQL
```bash
# Ubuntu
sudo apt install mysql-server -y

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

#### 1.4 安装Nginx
```bash
# Ubuntu
sudo apt install nginx -y

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### 1.5 安装PM2
```bash
sudo npm install -g pm2
```

### 2. 数据库配置

#### 2.1 创建数据库和用户
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE ziguan_wei CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'ziguan_user'@'localhost' IDENTIFIED BY 'your_strong_password';

-- 授权
GRANT ALL PRIVILEGES ON ziguan_wei.* TO 'ziguan_user'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

#### 2.2 导入数据库结构
```bash
# 导入初始数据库结构
mysql -u ziguan_user -p ziguan_wei < mysql_schema.sql

# 执行迁移脚本
mysql -u ziguan_user -p ziguan_wei < database_migration.sql
```

### 3. 后端部署

#### 3.1 克隆代码
```bash
# 创建应用目录
sudo mkdir -p /var/www/ziguan-wei
cd /var/www/ziguan-wei

# 克隆代码（假设使用Git）
git clone https://github.com/your-org/ziguan-wei-backend.git backend
cd backend
```

#### 3.2 安装依赖
```bash
npm install --production
```

#### 3.3 配置环境变量
```bash
# 创建环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

`.env` 文件内容：
```env
# 应用配置
NODE_ENV=production
PORT=3000
APP_NAME=资管维平台

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=ziguan_wei
DB_USER=ziguan_user
DB_PASSWORD=your_strong_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 微信小程序配置
WECHAT_APPID=your_wechat_appid
WECHAT_SECRET=your_wechat_secret

# 文件上传配置
UPLOAD_PATH=/var/www/ziguan-wei/uploads
MAX_FILE_SIZE=10485760

# 邮件配置（可选）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_smtp_password
```

#### 3.4 创建上传目录
```bash
sudo mkdir -p /var/www/ziguan-wei/uploads
sudo chown -R www-data:www-data /var/www/ziguan-wei/uploads
sudo chmod -R 755 /var/www/ziguan-wei/uploads
```

#### 3.5 使用PM2启动应用
```bash
# 创建PM2配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'ziguan-wei-api',
    script: './src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# 创建日志目录
mkdir -p logs

# 启动应用
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 4. Nginx配置

#### 4.1 创建Nginx配置文件
```bash
sudo nano /etc/nginx/sites-available/ziguan-wei
```

配置内容：
```nginx
server {
    listen 80;
    server_name api.ziguan-wei.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.ziguan-wei.com;

    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # 日志配置
    access_log /var/log/nginx/ziguan-wei-access.log;
    error_log /var/log/nginx/ziguan-wei-error.log;

    # API代理
    location /api/ {
        proxy_pass http://localhost:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 文件上传
    location /uploads/ {
        alias /var/www/ziguan-wei/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 健康检查
    location /health {
        proxy_pass http://localhost:3000/health;
        access_log off;
    }

    # 限制请求大小
    client_max_body_size 10M;
}
```

#### 4.2 启用站点
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/ziguan-wei /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 5. SSL证书配置

#### 5.1 使用Let's Encrypt（免费）
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取证书
sudo certbot --nginx -d api.ziguan-wei.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 6. 微信小程序部署

#### 6.1 用户端小程序
1. 使用微信开发者工具打开 `miniprogram-user` 目录
2. 修改 `app.js` 中的 `baseUrl` 为生产环境地址
3. 配置小程序AppID和AppSecret
4. 上传代码并提交审核

#### 6.2 工程师端小程序
1. 使用微信开发者工具打开 `miniprogram-service` 目录
2. 修改 `app.js` 中的 `baseUrl` 为生产环境地址
3. 配置小程序AppID和AppSecret
4. 上传代码并提交审核

### 7. 监控和日志

#### 7.1 设置日志轮转
```bash
sudo nano /etc/logrotate.d/ziguan-wei
```

内容：
```
/var/www/ziguan-wei/backend/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload ziguan-wei-api
    endscript
}
```

#### 7.2 设置监控
```bash
# 安装监控工具
pm2 install pm2-server-monit

# 查看应用状态
pm2 status
pm2 logs
pm2 monit
```

### 8. 安全配置

#### 8.1 防火墙配置
```bash
# 启用UFW防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 查看状态
sudo ufw status
```

#### 8.2 定期备份
```bash
# 创建备份脚本
sudo nano /usr/local/bin/backup-ziguan-wei.sh
```

备份脚本内容：
```bash
#!/bin/bash
BACKUP_DIR="/backup/ziguan-wei"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u ziguan_user -p'your_password' ziguan_wei > $BACKUP_DIR/db_$DATE.sql

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz /var/www/ziguan-wei/uploads

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

```bash
# 设置执行权限
sudo chmod +x /usr/local/bin/backup-ziguan-wei.sh

# 添加到定时任务
sudo crontab -e
# 添加：每天凌晨2点备份
# 0 2 * * * /usr/local/bin/backup-ziguan-wei.sh
```

## 验证部署

### 1. 检查服务状态
```bash
# 检查PM2应用状态
pm2 status

# 检查Nginx状态
sudo systemctl status nginx

# 检查MySQL状态
sudo systemctl status mysql
```

### 2. 测试API接口
```bash
# 健康检查
curl https://api.ziguan-wei.com/health

# 测试登录接口
curl -X POST https://api.ziguan-wei.com/api/auth/test \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

### 3. 检查日志
```bash
# 查看应用日志
pm2 logs ziguan-wei-api

# 查看Nginx日志
sudo tail -f /var/log/nginx/ziguan-wei-access.log
sudo tail -f /var/log/nginx/ziguan-wei-error.log
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查防火墙设置

2. **PM2应用启动失败**
   - 检查Node.js版本
   - 验证环境变量
   - 查看错误日志

3. **Nginx代理错误**
   - 检查配置文件语法
   - 验证上游服务状态
   - 查看错误日志

4. **SSL证书问题**
   - 检查证书有效期
   - 验证域名配置
   - 更新证书

## 性能优化

1. **数据库优化**
   - 配置合适的缓冲池大小
   - 创建必要的索引
   - 定期分析表

2. **应用优化**
   - 启用集群模式
   - 配置缓存
   - 优化查询

3. **Nginx优化**
   - 启用gzip压缩
   - 配置缓存
   - 调整worker进程数

## 维护建议

1. **定期更新**
   - 系统安全更新
   - 依赖包更新
   - 应用版本更新

2. **监控告警**
   - 设置资源监控
   - 配置错误告警
   - 监控业务指标

3. **备份策略**
   - 数据库定期备份
   - 文件定期备份
   - 测试恢复流程

## 联系支持

如果在部署过程中遇到问题，请联系技术支持团队。
